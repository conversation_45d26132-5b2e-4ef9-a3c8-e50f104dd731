<template>
  <div class="android-page">
    <div class="page-header">
      <h1 class="page-title">Android端管理</h1>
      <p class="page-description">管理Android设备，安装应用，执行ADB命令</p>
    </div>

    <div class="content-grid">
      <!-- 设备管理 -->
      <el-card class="device-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">设备管理</span>
            <el-button size="small" @click="refreshDevices">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>

        <div v-if="loading.devices" class="loading-container">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <div class="loading-text">正在获取设备列表...</div>
        </div>

        <div v-else-if="devices.length === 0" class="empty-container">
          <el-icon class="empty-icon"><Iphone /></el-icon>
          <div class="empty-text">未发现Android设备</div>
          <div class="empty-description">请确保设备已连接并开启USB调试</div>
        </div>

        <div v-else class="device-list">
          <div
            v-for="device in devices"
            :key="device.device_id"
            class="device-item"
            :class="{ active: device.is_active }"
            @click="selectDevice(device)"
          >
            <div class="device-info">
              <div class="device-name">{{ device.device_name }}</div>
              <div class="device-details">
                <el-tag size="small" :type="device.is_connected ? 'success' : 'danger'">
                  {{ device.is_connected ? '已连接' : '未连接' }}
                </el-tag>
                <span class="device-id">{{ device.device_id }}</span>
              </div>
              <div v-if="device.android_version" class="device-specs">
                Android {{ device.android_version }}
                <span v-if="device.screen_resolution">• {{ device.screen_resolution }}</span>
              </div>
            </div>
            <div v-if="device.is_active" class="active-indicator">
              <el-icon color="#67C23A"><Check /></el-icon>
            </div>
          </div>
        </div>
      </el-card>

      <!-- APK安装 -->
      <el-card class="apk-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">APK安装</span>
          </div>
        </template>

        <el-form :model="apkForm" label-width="80px">
          <el-form-item label="下载URL">
            <el-input
              v-model="apkForm.downloadUrl"
              placeholder="请输入APK下载链接"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :loading="loading.apk"
              @click="installApk"
              :disabled="!apkForm.downloadUrl || !hasActiveDevice"
            >
              <el-icon v-if="!loading.apk"><Download /></el-icon>
              {{ loading.apk ? '创建任务中...' : '下载并安装' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- APK安装任务列表 -->
        <div v-if="apkTasks.length > 0" class="apk-tasks">
          <h4>安装任务</h4>
          <div v-for="task in apkTasks" :key="task.task_id" class="task-item">
            <div class="task-header">
              <span class="task-filename">{{ task.filename }}</span>
              <el-tag :type="getTaskStatusType(task.status)">{{ getTaskStatusText(task.status) }}</el-tag>
            </div>

            <div v-if="task.status === 'downloading' || task.status === 'installing'" class="task-progress">
              <el-progress
                :percentage="task.progress"
                :status="task.status === 'installing' ? 'warning' : 'primary'"
                :show-text="true"
              />
              <div class="progress-info">
                <span v-if="task.download_speed">{{ task.download_speed }}</span>
                <span v-if="task.total_size && task.downloaded_size">
                  {{ formatFileSize(task.downloaded_size) }} / {{ formatFileSize(task.total_size) }}
                </span>
              </div>
            </div>

            <div v-if="task.error_message" class="task-error">
              <el-alert :title="task.error_message" type="error" :closable="false" />
            </div>
          </div>
        </div>

        <div v-if="!hasActiveDevice" class="warning-tip">
          <el-alert
            title="请先选择一个活动设备"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>

      <!-- ADB命令 -->
      <el-card class="adb-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">ADB命令</span>
          </div>
        </template>

        <el-form :model="adbForm" label-width="80px">
          <el-form-item label="命令">
            <el-input
              v-model="adbForm.command"
              placeholder="请输入ADB命令 (不需要包含 adb -s device_id)"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :loading="loading.adb"
              @click="executeAdbCommand"
              :disabled="!adbForm.command || !hasActiveDevice"
            >
              <el-icon v-if="!loading.adb"><Monitor /></el-icon>
              {{ loading.adb ? '执行中...' : '执行命令' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 命令结果 -->
        <div v-if="adbResult" class="adb-result">
          <el-divider>执行结果</el-divider>
          <div class="result-header">
            <span class="command-text">{{ adbResult.command }}</span>
            <el-tag :type="adbResult.success ? 'success' : 'danger'" size="small">
              {{ adbResult.success ? '成功' : '失败' }}
            </el-tag>
          </div>
          <div v-if="adbResult.stdout" class="result-content">
            <h4>输出:</h4>
            <pre class="output-text">{{ adbResult.stdout }}</pre>
          </div>
          <div v-if="adbResult.stderr" class="result-content error">
            <h4>错误:</h4>
            <pre class="output-text">{{ adbResult.stderr }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 设备截图 -->
      <el-card class="screenshot-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">设备截图</span>
            <div class="header-buttons">
              <el-button
                size="small"
                @click="takeScreenshot"
                :loading="loading.screenshot"
                :disabled="!hasActiveDevice"
              >
                <el-icon v-if="!loading.screenshot"><Camera /></el-icon>
                {{ loading.screenshot ? '截图中...' : '截图' }}
              </el-button>
              <el-button
                size="small"
                type="success"
                @click="takePocoScreenshot"
                :loading="loading.pocoScreenshot"
                :disabled="!hasActiveDevice"
              >
                <el-icon v-if="!loading.pocoScreenshot"><View /></el-icon>
                {{ loading.pocoScreenshot ? '获取中...' : 'Poco截图' }}
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="screenshot" class="screenshot-container">
          <img :src="screenshot.url" alt="设备截图" class="screenshot-image" />
          <div class="screenshot-info">
            <span>截图时间: {{ formatTime(screenshot.timestamp) }}</span>
          </div>
        </div>

        <div v-else class="empty-container">
          <el-icon class="empty-icon"><Picture /></el-icon>
          <div class="empty-text">暂无截图</div>
          <div class="empty-description">点击截图按钮获取设备屏幕</div>
        </div>
      </el-card>

      <!-- DOM索引查询模块 -->
      <el-card class="dom-query-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">DOM索引查询</span>
          </div>
        </template>

        <DomIndexQuery
          type="android"
          :device-id="devices.find(d => d.is_active)?.device_id"
          @element-click="onDomElementClick"
        />

        <div v-if="!hasActiveDevice" class="warning-tip">
          <el-alert
            title="请先选择一个活动设备"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>

      <!-- 调试操作模块 -->
      <div class="debug-operations-card">
        <DebugOperations platform="android" />

        <div v-if="!hasActiveDevice" class="warning-tip">
          <el-alert
            title="请先选择一个活动设备"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <!-- Poco DOM树模块 -->
      <el-card class="dom-tree-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">Poco DOM树</span>
            <div class="header-buttons">
              <el-button
                size="small"
                @click="getPocoDomTree"
                :loading="loading.domTree"
                :disabled="!hasActiveDevice"
              >
                <el-icon v-if="!loading.domTree"><Document /></el-icon>
                {{ loading.domTree ? '获取中...' : '获取DOM树' }}
              </el-button>
              <el-button
                v-if="domTree"
                size="small"
                type="info"
                @click="expandAllNodes"
              >
                展开全部
              </el-button>
              <el-button
                v-if="domTree"
                size="small"
                type="info"
                @click="collapseAllNodes"
              >
                收起全部
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="domTree" class="dom-tree-display">
          <div class="dom-tree-stats">
            <el-tag type="info">节点总数: {{ domTreeStats.totalNodes }}</el-tag>
            <el-tag type="success">可点击: {{ domTreeStats.clickableNodes }}</el-tag>
            <el-tag type="warning">有文本: {{ domTreeStats.textNodes }}</el-tag>
          </div>

          <div class="dom-tree-container">
            <DomTreeNode
              :node="domTree"
              :level="0"
              @node-click="onDomNodeClick"
              ref="domTreeRoot"
            />
          </div>
        </div>

        <div v-else class="empty-container">
          <el-icon class="empty-icon"><Document /></el-icon>
          <div class="empty-text">暂无DOM树</div>
          <div class="empty-description">点击获取DOM树按钮获取页面结构</div>
        </div>

        <div v-if="!hasActiveDevice" class="warning-tip">
          <el-alert
            title="请先选择一个活动设备"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>

      <!-- 实时任务执行 -->
      <el-card class="execution-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">实时任务执行</span>
            <el-button
              v-if="executionStatus.is_running"
              size="small"
              type="danger"
              @click="stopExecution"
              :loading="loading.stop"
            >
              <el-icon v-if="!loading.stop"><Close /></el-icon>
              {{ loading.stop ? '停止中...' : '停止执行' }}
            </el-button>
          </div>
        </template>

        <div v-if="!hasActiveDevice" class="warning-tip">
          <el-alert
            title="请先选择一个活动设备"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>

        <div v-else>
          <!-- 任务输入 -->
          <el-form :model="taskForm" label-width="80px">
            <el-form-item label="输入方式">
              <el-radio-group v-model="taskForm.inputType">
                <el-radio value="text">文本输入</el-radio>
                <el-radio value="voice" disabled>语音输入</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="任务描述">
              <el-input
                v-model="taskForm.inputContent"
                type="textarea"
                :rows="4"
                placeholder="请描述您希望在Android设备上执行的任务，例如：打开微信，发送消息给张三"
                :disabled="executionStatus.is_running"
              />
            </el-form-item>

            <el-form-item label="AI配置">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-input
                    v-model="taskForm.aiModel"
                    placeholder="AI模型 (可选)"
                    :disabled="executionStatus.is_running"
                  />
                </el-col>
                <el-col :span="12">
                  <el-input-number
                    v-model="taskForm.temperature"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    placeholder="温度参数"
                    :disabled="executionStatus.is_running"
                    style="width: 100%"
                  />
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="loading.execution"
                @click="executeTask"
                :disabled="!taskForm.inputContent || executionStatus.is_running"
                style="width: 100%"
              >
                <el-icon v-if="!loading.execution"><VideoPlay /></el-icon>
                {{ loading.execution ? '执行中...' : '开始执行任务' }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 执行状态 -->
          <div v-if="executionStatus.is_running" class="execution-status">
            <el-divider>执行状态</el-divider>
            <div class="status-info">
              <el-tag type="success" size="large">
                <el-icon><Loading /></el-icon>
                正在执行中...
              </el-tag>
              <div class="status-details">
                <p>运行时间: {{ formatDuration(executionStatus.running_time) }}</p>
                <p>已完成步骤: {{ executionStatus.completed_steps }}</p>
              </div>
            </div>
          </div>

          <!-- 执行结果 -->
          <div v-if="executionResult" class="execution-result">
            <el-divider>执行结果</el-divider>
            <div class="result-header">
              <el-tag :type="executionResult.success ? 'success' : 'danger'" size="large">
                {{ executionResult.success ? '执行成功' : '执行失败' }}
              </el-tag>
              <span class="execution-time">
                耗时: {{ formatDuration(executionResult.execution_time) }}
              </span>
            </div>
            <div class="result-message">
              {{ executionResult.message }}
            </div>
            <div v-if="!executionResult.success && executionResult.error" class="error-details">
              <h4>错误详情:</h4>
              <pre>{{ executionResult.error }}</pre>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { androidApi } from '@/api/android'
import DomTreeNode from '@/components/DomTreeNode.vue'
import DomIndexQuery from '@/components/DomIndexQuery.vue'
import DebugOperations from '@/components/DebugOperations.vue'
import {
  Iphone,
  Refresh,
  Check,
  Download,
  Monitor,
  Camera,
  Picture,
  Close,
  VideoPlay,
  Loading,
  View,
  Document
} from '@element-plus/icons-vue'

// 响应式数据
const devices = ref<any[]>([])
const loading = reactive({
  devices: false,
  apk: false,
  adb: false,
  screenshot: false,
  pocoScreenshot: false,
  domTree: false,
  execution: false,
  stop: false
})

const apkForm = reactive({
  downloadUrl: ''
})

const adbForm = reactive({
  command: ''
})

const taskForm = reactive({
  inputType: 'text' as 'text' | 'voice',
  inputContent: '',
  aiModel: '',
  temperature: 0.7
})

const adbResult = ref<any>(null)
const screenshot = ref<any>(null)
const domTree = ref<any>(null)
const domTreeRoot = ref<any>(null)
const executionStatus = ref({
  is_running: false,
  running_time: 0,
  completed_steps: 0
})

// APK安装任务相关
const apkTasks = ref<any[]>([])
const taskPollingInterval = ref<number | null>(null)
const executionResult = ref(null)

// 计算属性
const hasActiveDevice = computed(() => {
  return devices.value.some(device => device.is_active)
})

const domTreeStats = computed(() => {
  if (!domTree.value) {
    return { totalNodes: 0, clickableNodes: 0, textNodes: 0 }
  }

  const stats = { totalNodes: 0, clickableNodes: 0, textNodes: 0 }

  const countNodes = (node: any) => {
    stats.totalNodes++

    if (node.payload) {
      // 检查是否可点击
      if (node.payload.clickable || node.payload.enabled) {
        stats.clickableNodes++
      }

      // 检查是否有文本
      if (node.payload.text && node.payload.text.trim()) {
        stats.textNodes++
      }
    }

    if (node.children && node.children.length > 0) {
      node.children.forEach(countNodes)
    }
  }

  countNodes(domTree.value)
  return stats
})

// 方法
const refreshDevices = async () => {
  loading.devices = true
  try {
    const response = await androidApi.getDevices()
    devices.value = response
  } catch (error) {
    ElMessage.error('获取设备列表失败')
  } finally {
    loading.devices = false
  }
}

const selectDevice = async (device) => {
  if (!device.is_connected) {
    ElMessage.warning('设备未连接')
    return
  }

  try {
    const response = await androidApi.selectDevice(device.device_id)
    if (response.success) {
      ElMessage.success(response.message)
      await refreshDevices()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('选择设备失败')
  }
}

const installApk = async () => {
  loading.apk = true
  try {
    const response = await androidApi.installApk({
      download_url: apkForm.downloadUrl
    })

    if (response.success) {
      ElMessage.success(response.message)
      apkForm.downloadUrl = ''

      // 开始轮询任务状态
      if (response.task_id) {
        startTaskPolling(response.task_id)
      }

      // 刷新任务列表
      await loadApkTasks()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('创建安装任务失败')
  } finally {
    loading.apk = false
  }
}

// APK任务相关方法
const loadApkTasks = async () => {
  try {
    const response = await androidApi.getApkInstallTasks()
    if (response.success) {
      apkTasks.value = response.tasks
    }
  } catch (error) {
    console.error('获取APK任务列表失败:', error)
  }
}

const startTaskPolling = (taskId: string) => {
  const pollTask = async () => {
    try {
      const response = await androidApi.getApkInstallStatus(taskId)
      if (response.success && response.task_id) {
        // 更新任务列表中的对应任务
        const taskIndex = apkTasks.value.findIndex(t => t.task_id === taskId)
        if (taskIndex !== -1) {
          apkTasks.value[taskIndex] = {
            ...apkTasks.value[taskIndex],
            status: response.status,
            progress: response.progress,
            total_size: response.total_size,
            downloaded_size: response.downloaded_size,
            download_speed: response.download_speed,
            error_message: response.error_message
          }
        }

        // 如果任务完成或失败，停止轮询
        if (response.status === 'completed' || response.status === 'failed') {
          if (response.status === 'completed') {
            ElMessage.success('APK安装完成')
          } else {
            ElMessage.error(`APK安装失败: ${response.error_message}`)
          }
          return // 停止轮询
        }

        // 继续轮询
        setTimeout(pollTask, 2000)
      }
    } catch (error) {
      console.error('轮询任务状态失败:', error)
    }
  }

  // 开始轮询
  setTimeout(pollTask, 1000)
}

// 任务状态相关方法
const getTaskStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'failed': return 'danger'
    case 'downloading': return 'primary'
    case 'installing': return 'warning'
    default: return 'info'
  }
}

const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '等待中'
    case 'downloading': return '下载中'
    case 'installing': return '安装中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    default: return '未知'
  }
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const executeAdbCommand = async () => {
  loading.adb = true
  try {
    const response = await androidApi.executeAdbCommand({
      command: adbForm.command
    })
    
    adbResult.value = response
    
    if (response.success) {
      ElMessage.success('命令执行成功')
    } else {
      ElMessage.error('命令执行失败')
    }
  } catch (error) {
    ElMessage.error('执行ADB命令失败')
  } finally {
    loading.adb = false
  }
}

const takeScreenshot = async () => {
  const activeDevice = devices.value.find(device => device.is_active)
  if (!activeDevice) {
    ElMessage.warning('请先选择一个活动设备')
    return
  }

  loading.screenshot = true
  try {
    const response = await androidApi.takeScreenshot(activeDevice.device_id)

    if (response.success) {
      screenshot.value = {
        url: response.url,
        timestamp: Date.now()
      }
      ElMessage.success('截图成功')
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('截图失败')
  } finally {
    loading.screenshot = false
  }
}

const takePocoScreenshot = async () => {
  const activeDevice = devices.value.find(device => device.is_active)
  if (!activeDevice) {
    ElMessage.warning('请先选择一个活动设备')
    return
  }

  loading.pocoScreenshot = true
  try {
    const response = await androidApi.getScreenshotWithPoco(activeDevice.device_id)

    if (response.success) {
      screenshot.value = {
        url: response.url,
        timestamp: Date.now()
      }
      domTree.value = response.dom_tree
      ElMessage.success('Poco截图获取成功')
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('Poco截图失败')
  } finally {
    loading.pocoScreenshot = false
  }
}

const getPocoDomTree = async () => {
  const activeDevice = devices.value.find(device => device.is_active)
  if (!activeDevice) {
    ElMessage.warning('请先选择一个活动设备')
    return
  }

  loading.domTree = true
  try {
    const response = await androidApi.getPocoDomTree(activeDevice.device_id)

    if (response.success) {
      domTree.value = response.dom_tree
      ElMessage.success('DOM树获取成功')
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('获取DOM树失败')
  } finally {
    loading.domTree = false
  }
}

const expandAllNodes = () => {
  if (domTreeRoot.value && domTreeRoot.value.expandAll) {
    domTreeRoot.value.expandAll()
  }
}

const collapseAllNodes = () => {
  if (domTreeRoot.value && domTreeRoot.value.collapseAll) {
    domTreeRoot.value.collapseAll()
  }
}

const onDomNodeClick = (node: any) => {
  console.log('DOM节点点击:', node)
  ElMessage.info(`点击节点: ${node.seq_index} - ${node.payload?.text || '无文本'}`)
}

const onDomElementClick = (element: any) => {
  ElMessage.info(`选中元素: ${element.seq_index} - ${element.payload?.text || '无文本'}`)
  // 这里可以添加更多的元素操作逻辑
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const formatDuration = (seconds) => {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return `${minutes}分${remainingSeconds}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// 实时执行相关方法
const executeTask = async () => {
  loading.execution = true
  executionResult.value = null

  try {
    // 导入任务API
    const { taskApi } = await import('@/api/task')

    const response = await taskApi.executeDirectly({
      automation_type: 'android',
      input_type: taskForm.inputType,
      input_content: taskForm.inputContent,
      ai_model: taskForm.aiModel || undefined,
      temperature: taskForm.temperature,
      max_scroll_times: 3
    })

    if (response.success) {
      ElMessage.success('任务执行完成')
      executionResult.value = response

      // 清空表单
      taskForm.inputContent = ''
    } else {
      ElMessage.error(response.message || '任务执行失败')
      executionResult.value = response
    }
  } catch (error) {
    ElMessage.error('任务执行失败')
    executionResult.value = {
      success: false,
      message: '任务执行失败',
      error: error.message || '未知错误'
    }
  } finally {
    loading.execution = false
    executionStatus.value.is_running = false
  }
}

const stopExecution = async () => {
  loading.stop = true

  try {
    const { taskApi } = await import('@/api/task')
    const response = await taskApi.stopExecution()

    if (response.success) {
      ElMessage.success('任务已停止')
      executionStatus.value.is_running = false
    } else {
      ElMessage.error(response.message || '停止任务失败')
    }
  } catch (error) {
    ElMessage.error('停止任务失败')
  } finally {
    loading.stop = false
  }
}

const checkExecutionStatus = async () => {
  try {
    const { taskApi } = await import('@/api/task')
    const response = await taskApi.getExecutionStatus()

    if (response.is_running) {
      executionStatus.value = response
    } else {
      executionStatus.value.is_running = false
    }
  } catch (error) {
    // 静默处理错误，避免频繁提示
    console.error('获取执行状态失败:', error)
  }
}

// 组件挂载
onMounted(() => {
  refreshDevices()
  loadApkTasks()

  // 定期检查执行状态
  const statusInterval = setInterval(() => {
    if (executionStatus.value.is_running) {
      checkExecutionStatus()
    }
  }, 2000)

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(statusInterval)
  })
})
</script>

<style lang="scss" scoped>
.android-page {
  padding: 24px;
  height: 100%;
  overflow: auto;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

// 固定卡片高度和内部滚动
.el-card {
  height: 500px;
  display: flex;
  flex-direction: column;

  :deep(.el-card__body) {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }
}

.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);

  .loading-icon, .empty-icon {
    font-size: 24px;
    margin-bottom: 12px;
  }

  .loading-icon {
    animation: spin 1s linear infinite;
  }

  .loading-text, .empty-text {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .empty-description {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.device-list {
  .device-item {
    padding: 16px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }
    
    &.active {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }
    
    .device-info {
      flex: 1;
      
      .device-name {
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--el-text-color-primary);
      }
      
      .device-details {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        
        .device-id {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          font-family: monospace;
        }
      }
      
      .device-specs {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
    
    .active-indicator {
      font-size: 20px;
    }
  }
}

.warning-tip {
  margin-top: 16px;
}

/* APK任务样式 */
.apk-tasks {
  margin-top: 20px;
}

.task-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fafafa;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-filename {
  font-weight: 500;
  color: #303133;
}

.task-progress {
  margin-bottom: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.task-error {
  margin-top: 8px;
}

.adb-result {
  margin-top: 16px;
  
  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .command-text {
      font-family: monospace;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
  
  .result-content {
    margin-bottom: 16px;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
    
    .output-text {
      background: var(--el-bg-color-page);
      padding: 12px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      line-height: 1.4;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
    }
    
    &.error .output-text {
      background: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
    }
  }
}

.screenshot-container {
  text-align: center;
  
  .screenshot-image {
    max-width: 100%;
    max-height: 400px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .screenshot-info {
    margin-top: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.execution-status {
  margin-top: 16px;

  .status-info {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 12px;

    .status-details {
      flex: 1;

      p {
        margin: 4px 0;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.execution-result {
  margin-top: 16px;

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .execution-time {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }

  .result-message {
    padding: 12px;
    background: var(--el-bg-color-page);
    border-radius: 4px;
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.5;
  }

  .error-details {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: var(--el-color-danger);
    }

    pre {
      background: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
      padding: 12px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      line-height: 1.4;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}

// DOM树样式
.header-buttons {
  display: flex;
  gap: 8px;
}

.dom-tree-card {
  margin-bottom: 20px;
}

.dom-tree-display {
  max-height: 600px;
  overflow-y: auto;
}

.dom-tree-stats {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.dom-tree-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background: #fff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}

// 调试操作模块样式
.debug-operations-card {
  position: relative;
}

.debug-operations-card .warning-tip {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .header-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
