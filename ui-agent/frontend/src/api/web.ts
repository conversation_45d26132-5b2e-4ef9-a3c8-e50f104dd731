/**
 * Web端API
 */

import { request } from '@/utils/request'

export interface NavigateRequest {
  url: string
  cookies?: Array<{ name: string; value: string; domain?: string; path?: string }>
  headers?: Record<string, string>
  is_h5_mode?: boolean
}

export interface SetCookiesRequest {
  cookies: Array<{ name: string; value: string; domain?: string; path?: string }>
}

export interface SetHeadersRequest {
  headers: Record<string, string>
}

export interface ExecuteJsRequest {
  code: string
}

export interface BrowserStatus {
  success: boolean
  status: string
  current_url?: string
  title?: string
  viewport?: { width: number; height: number }
  session_id?: string
  message?: string
}

export const webApi = {
  /**
   * 导航到指定URL
   */
  navigate: (data: NavigateRequest): Promise<{
    success: boolean
    message: string
    session_id?: string
    current_url?: string
    title?: string
  }> => {
    return request.post('/api/web/navigate', data)
  },

  /**
   * 设置Cookie
   */
  setCookies: (data: SetCookiesRequest): Promise<{
    success: boolean
    message: string
    cookies_count?: number
  }> => {
    return request.post('/api/web/cookies', data)
  },

  /**
   * 设置Header
   */
  setHeaders: (data: SetHeadersRequest): Promise<{
    success: boolean
    message: string
    headers?: Record<string, string>
  }> => {
    return request.post('/api/web/headers', data)
  },

  /**
   * 执行JavaScript代码
   */
  executeJs: (data: ExecuteJsRequest): Promise<{
    success: boolean
    message: string
    result?: any
  }> => {
    return request.post('/api/web/execute_js', data)
  },

  /**
   * 重置浏览器实例
   */
  resetBrowser: (): Promise<{
    success: boolean
    message: string
  }> => {
    return request.post('/api/web/reset')
  },

  /**
   * 获取页面截图
   */
  takeScreenshot: (): Promise<{
    success: boolean
    file_path?: string
    url?: string
    timestamp?: number
    message?: string
  }> => {
    return request.get('/api/web/screenshot')
  },

  /**
   * 获取浏览器状态
   */
  getBrowserStatus: (): Promise<BrowserStatus> => {
    return request.get('/api/web/status')
  },

  /**
   * 获取当前页面URL
   */
  getCurrentUrl: (): Promise<{
    success: boolean
    url?: string
    title?: string
    message?: string
  }> => {
    return request.get('/api/web/current_url')
  },

  /**
   * 通过坐标点击
   */
  clickByCoordinate: (x: number, y: number): Promise<{
    success: boolean
    message: string
    action?: string
    coordinates?: { x: number; y: number }
  }> => {
    return request.post('/api/web/click_coordinate', { x, y })
  },

  /**
   * 通过seq_index点击元素
   */
  clickBySeqIndex: (seqIndex: number): Promise<{
    success: boolean
    message: string
    action?: string
    seq_index?: number
    coordinates?: { x: number; y: number }
    element_info?: any
  }> => {
    return request.post('/api/web/click_seq_index', { seq_index: seqIndex })
  },

  /**
   * 输入文本
   */
  inputText: (text: string, options?: { seq_index?: number; x?: number; y?: number }): Promise<{
    success: boolean
    message: string
    action?: string
    text?: string
  }> => {
    return request.post('/api/web/input_text', { text, ...options })
  },

  /**
   * 滚动页面
   */
  scrollPage: (direction: string = 'down', distance: number = 500): Promise<{
    success: boolean
    message: string
    action?: string
    direction?: string
    distance?: number
  }> => {
    return request.post('/api/web/scroll', { direction, distance })
  },

  /**
   * 按键操作
   */
  pressKey: (key: string): Promise<{
    success: boolean
    message: string
    action?: string
    key?: string
  }> => {
    return request.post('/api/web/press_key', { key })
  },

  /**
   * 等待元素出现
   */
  waitForElement: (selector: string, timeout: number = 5000): Promise<{
    success: boolean
    message: string
    action?: string
    selector?: string
  }> => {
    return request.post('/api/web/wait_for_element', { selector, timeout })
  },

  /**
   * 获取DOM树和页面截图
   */
  getDomTreeAndScreenshot: (): Promise<{
    base64_image: string
    viewport: { width: number; height: number }
    is_scrollable: boolean
    dom_tree: any
    screenshot_path: string
    error?: string
  }> => {
    return request.post('/api/web/get_dom_tree_and_screenshot')
  },

  /**
   * 获取带DOM树标注的页面截图
   */
  takeScreenshotWithDom: (): Promise<{
    base64_image: string
    viewport: any
    is_scrollable: boolean
    dom_tree: any
    screenshot_path: string
    error?: string
  }> => {
    return request.get('/api/web/screenshot_with_dom')
  },

  /**
   * 获取页面DOM树
   */
  getDomTree: (): Promise<{
    success: boolean
    dom_tree?: any
    message: string
  }> => {
    return request.get('/api/web/dom_tree')
  },

  /**
   * 根据索引查询DOM元素信息
   */
  getDomElementByIndex: (seqIndex: number): Promise<{
    success: boolean
    element?: any
    message: string
  }> => {
    return request.post('/api/web/dom_element', { seq_index: seqIndex })
  }
}
