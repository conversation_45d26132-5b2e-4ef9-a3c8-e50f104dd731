"""
Android端API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.android_service import AndroidService

router = APIRouter()

# 全局Android服务实例，确保设备状态共享
_android_service_instance = None

def get_android_service(db: Session = Depends(get_db)) -> AndroidService:
    """获取Android服务实例（单例模式）"""
    global _android_service_instance
    if _android_service_instance is None:
        _android_service_instance = AndroidService(db)
    else:
        # 更新数据库会话
        _android_service_instance.db = db
    return _android_service_instance

class DeviceInfo(BaseModel):
    """设备信息模型"""
    device_id: str
    device_name: str
    brand: Optional[str] = None
    model: Optional[str] = None
    android_version: Optional[str] = None
    screen_resolution: Optional[str] = None
    is_connected: bool = False
    is_active: bool = False

class InstallApkRequest(BaseModel):
    """安装APK请求模型"""
    download_url: str
    device_id: Optional[str] = None

class AdbCommandRequest(BaseModel):
    """ADB命令请求模型"""
    command: str
    device_id: Optional[str] = None

@router.get("/devices", response_model=List[DeviceInfo])
async def get_devices(android_service: AndroidService = Depends(get_android_service)):
    """
    获取Android设备列表
    """
    try:
        devices = await android_service.get_devices()
        return devices
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取设备列表失败: {str(e)}")

@router.post("/devices/{device_id}/select")
async def select_device(device_id: str, android_service: AndroidService = Depends(get_android_service)):
    """
    选择当前活动设备
    """
    try:
        result = await android_service.select_device(device_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"选择设备失败: {str(e)}")

@router.post("/devices/refresh")
async def refresh_devices(android_service: AndroidService = Depends(get_android_service)):
    """
    刷新设备列表
    """
    try:
        devices = await android_service.refresh_devices()
        return {
            "success": True,
            "message": "设备列表已刷新",
            "devices": devices
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新设备列表失败: {str(e)}")

@router.post("/install_apk")
async def install_apk(request: InstallApkRequest, android_service: AndroidService = Depends(get_android_service)):
    """
    创建APK安装任务（异步）
    """
    try:
        result = await android_service.install_apk(
            download_url=request.download_url,
            device_id=request.device_id
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建安装任务失败: {str(e)}")

@router.get("/install_tasks/{task_id}/status")
async def get_apk_install_status(task_id: str, android_service: AndroidService = Depends(get_android_service)):
    """
    获取APK安装任务状态
    """
    try:
        result = await android_service.get_apk_install_status(task_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.get("/install_tasks")
async def get_apk_install_tasks(device_id: Optional[str] = None, android_service: AndroidService = Depends(get_android_service)):
    """
    获取APK安装任务列表
    """
    try:
        tasks = await android_service.get_apk_install_tasks(device_id)
        return {"success": True, "tasks": tasks}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.get("/poco_dom_tree")
async def get_poco_dom_tree(device_id: Optional[str] = None, android_service: AndroidService = Depends(get_android_service)):
    """
    获取poco DOM树
    """
    try:
        result = await android_service.get_poco_dom_tree(device_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取poco DOM树失败: {str(e)}")

@router.get("/screenshot_with_poco")
async def take_screenshot_with_poco(device_id: Optional[str] = None, android_service: AndroidService = Depends(get_android_service)):
    """
    获取带poco DOM树标注的截图
    """
    try:
        result = await android_service.take_screenshot_with_poco_dom(device_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取poco截图失败: {str(e)}")

@router.post("/adb_command")
async def execute_adb_command(request: AdbCommandRequest, android_service: AndroidService = Depends(get_android_service)):
    """
    执行ADB命令
    """
    try:
        result = await android_service.execute_adb_command(
            command=request.command,
            device_id=request.device_id
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行ADB命令失败: {str(e)}")

@router.get("/devices/{device_id}/screenshot")
async def take_screenshot(device_id: str, android_service: AndroidService = Depends(get_android_service)):
    """
    获取设备截图
    """
    try:
        result = await android_service.take_screenshot(device_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取截图失败: {str(e)}")

@router.get("/devices/{device_id}/status")
async def get_device_status(device_id: str, android_service: AndroidService = Depends(get_android_service)):
    """
    获取设备状态
    """
    try:
        status = await android_service.get_device_status(device_id)
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取设备状态失败: {str(e)}")

class DomElementQueryRequest(BaseModel):
    """DOM元素查询请求模型"""
    seq_index: int
    device_id: Optional[str] = None

@router.post("/dom_element")
async def get_dom_element_by_index(request: DomElementQueryRequest, android_service: AndroidService = Depends(get_android_service)):
    """
    根据索引查询DOM元素信息
    """
    try:
        result = await android_service.get_dom_element_by_index(request.seq_index, request.device_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询DOM元素失败: {str(e)}")
