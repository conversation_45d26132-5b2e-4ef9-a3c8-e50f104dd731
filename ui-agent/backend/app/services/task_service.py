"""
实时任务执行服务
"""

import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.models.task import Task, TaskStep, TaskLog, TaskHistory, ExecutionStep
from app.services.ai_service import AIService
from app.services.android_service import AndroidService
from app.services.web_service import WebService
from app.core.config import settings

class TaskService:
    """实时任务执行服务类"""

    def __init__(self, db: Session, android_service=None, web_service=None):
        self.db = db
        self.ai_service = AIService(db)
        # 使用传入的服务实例或创建新的
        self.android_service = android_service or AndroidService(db)
        self.web_service = web_service or WebService(db)
        self.current_execution = None  # 当前执行状态

    async def execute_task_directly(
        self,
        automation_type: str,
        input_type: str,
        input_content: str,
        ai_model: Optional[str] = None,
        api_base: Optional[str] = None,
        temperature: Optional[float] = 0.7,
        max_scroll_times: Optional[int] = 3
    ) -> Dict[str, Any]:
        """直接执行任务（实时执行模式）"""
        start_time = time.time()

        try:
            # 创建任务历史记录
            history = TaskHistory(
                automation_type=automation_type,
                input_type=input_type,
                input_content=input_content,
                ai_model=ai_model or settings.DEFAULT_AI_MODEL,
                api_base=api_base or settings.DEFAULT_API_BASE,
                temperature=temperature,
                status="running"
            )

            self.db.add(history)
            self.db.commit()
            self.db.refresh(history)

            # 设置当前执行状态
            self.current_execution = {
                "history_id": history.id,
                "status": "running",
                "start_time": start_time
            }

            # 验证设备/浏览器状态
            if automation_type == "android":
                device_status = await self.android_service.get_current_device_status()
                if not device_status.get("is_connected"):
                    raise Exception("Android设备未连接，请先连接设备")
            elif automation_type == "web":
                browser_status = await self.web_service.get_browser_status_for_execution()
                if not browser_status.get("is_ready"):
                    raise Exception("Web浏览器未准备就绪，请先导航到页面")

            # 执行AI智能规划任务
            result = await self._handle_complex_ai_exec(
                history.id,
                automation_type,
                input_content,
                max_scroll_times
            )

            # 计算执行时间
            execution_time = time.time() - start_time

            # 更新历史记录
            history.status = "completed" if result.get("success") else "failed"
            history.result = result
            history.execution_time = execution_time
            history.completed_at = datetime.now()

            if not result.get("success"):
                history.error_message = result.get("error_message", "执行失败")

            self.db.commit()

            # 清除当前执行状态
            self.current_execution = None

            return {
                "success": result.get("success", False),
                "message": result.get("message", "任务执行完成"),
                "history_id": history.id,
                "execution_time": execution_time,
                "result": result
            }

        except Exception as e:
            # 更新历史记录为失败状态
            if 'history' in locals():
                history.status = "failed"
                history.error_message = str(e)
                history.execution_time = time.time() - start_time
                history.completed_at = datetime.now()
                self.db.commit()

            # 清除当前执行状态
            self.current_execution = None

            return {
                "success": False,
                "message": f"任务执行失败: {str(e)}",
                "error": str(e)
            }

    async def _handle_complex_ai_exec(
        self,
        history_id: int,
        automation_type: str,
        bdd_script: str,
        max_scroll_times: int = 3
    ) -> Dict[str, Any]:
        """处理AI智能规划执行（基于需求文档的流程）"""
        try:
            # 设置AI最多执行次数
            max_exec_times = 20
            last_result = ""
            last_step_result = ""
            step_index = 0

            for i in range(max_exec_times):
                # 调用AI服务获取下一步执行计划
                if automation_type == "web":
                    ai_response = await self.ai_service.process_complex_ai_exec_web(
                        desc=bdd_script,
                        last_result=last_result,
                        last_step_result=last_step_result,
                        max_scroll_times=max_scroll_times
                    )
                else:
                    ai_response = await self.ai_service.process_complex_ai_exec_android(
                        desc=bdd_script,
                        last_result=last_result,
                        last_step_result=last_step_result,
                        max_scroll_times=max_scroll_times
                    )

                # 解析AI响应
                if isinstance(ai_response, str):
                    ai_response = json.loads(ai_response)

                # 记录执行步骤
                step = ExecutionStep(
                    history_id=history_id,
                    step_index=step_index,
                    action_type=ai_response.get("next_executed_step", {}).get("code_info", {}).get("type", "unknown"),
                    description=ai_response.get("next_executed_step", {}).get("element_info", {}).get("description", ""),
                    ai_response=ai_response
                )

                self.db.add(step)
                self.db.commit()
                self.db.refresh(step)

                # 检查AI返回结果
                if not ai_response.get("step_list") or not ai_response.get("next_executed_step"):
                    step.success = False
                    step.error_message = "AI智能规划步骤为空"
                    self.db.commit()
                    raise Exception("AI智能规划步骤为空")

                # 检查是否完成
                if ai_response.get("result") == 0:
                    step.success = True
                    self.db.commit()
                    return {
                        "success": True,
                        "message": "任务执行完成",
                        "total_steps": step_index + 1
                    }

                # 执行具体操作
                exec_result = await self._execute_step(
                    automation_type,
                    ai_response.get("next_executed_step"),
                    step
                )

                if exec_result.get("success"):
                    last_result = ai_response
                    last_step_result = "true"
                    step.success = True
                else:
                    last_result = ai_response
                    last_step_result = "false"
                    step.success = False
                    step.error_message = exec_result.get("error_message", "执行失败")

                    # 如果是关键错误，停止执行
                    if exec_result.get("critical_error"):
                        self.db.commit()
                        raise Exception(exec_result.get("error_message", "执行失败"))

                self.db.commit()
                step_index += 1

            # 达到最大执行次数
            raise Exception("执行达到最大次数还没有完成")

        except Exception as e:
            return {
                "success": False,
                "message": f"AI执行失败: {str(e)}",
                "error_message": str(e)
            }

    async def _execute_step(
        self,
        automation_type: str,
        step_info: Dict[str, Any],
        step_record: ExecutionStep
    ) -> Dict[str, Any]:
        """执行具体的操作步骤"""
        try:
            element_info = step_info.get("element_info", {})
            code_info = step_info.get("code_info", {})

            # 检查元素查找状态
            find_status = element_info.get("find_status")

            if find_status == -1:
                return {
                    "success": False,
                    "error_message": "没有找到符合的元素",
                    "critical_error": True
                }

            if find_status == -2:
                # 需要滚动页面
                exec_code = code_info.get("code_generate", "")
                if automation_type == "web":
                    result = await self.web_service.execute_scroll_action(exec_code)
                else:
                    result = await self.android_service.execute_scroll_action(exec_code)

                return {
                    "success": result.get("success", False),
                    "error_message": result.get("error_message", "滚动操作失败")
                }

            # 处理断言和查找操作
            if code_info.get("type") in ["Assert", "Find"]:
                assert_result = code_info.get("assert_result", False)
                if not assert_result:
                    return {
                        "success": False,
                        "error_message": code_info.get("assert_thought", "断言失败"),
                        "critical_error": True
                    }
                return {"success": True}

            # 处理Action操作
            if code_info.get("type") == "Action":
                exec_code = code_info.get("code_generate", "")

                if automation_type == "web":
                    result = await self.web_service.execute_action(exec_code)
                else:
                    result = await self.android_service.execute_action(exec_code)

                # 获取截图
                if automation_type == "web":
                    screenshot_result = await self.web_service.take_screenshot_for_execution()
                else:
                    screenshot_result = await self.android_service.take_screenshot_for_execution()

                if screenshot_result.get("success"):
                    step_record.screenshot_path = screenshot_result.get("screenshot_path")

                return {
                    "success": result.get("success", False),
                    "error_message": result.get("error_message", "操作执行失败")
                }

            return {"success": True}

        except Exception as e:
            return {
                "success": False,
                "error_message": f"步骤执行异常: {str(e)}"
            }

    async def get_task_history(
        self,
        skip: int = 0,
        limit: int = 50,
        automation_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取任务执行历史"""
        try:
            query = self.db.query(TaskHistory)

            if automation_type:
                query = query.filter(TaskHistory.automation_type == automation_type)

            history_records = query.order_by(TaskHistory.created_at.desc()).offset(skip).limit(limit).all()

            return [
                {
                    "id": record.id,
                    "automation_type": record.automation_type,
                    "input_content": record.input_content,
                    "status": record.status,
                    "result": record.result,
                    "created_at": record.created_at.isoformat() if record.created_at else None,
                    "execution_time": record.execution_time
                }
                for record in history_records
            ]

        except Exception as e:
            raise Exception(f"获取任务历史失败: {str(e)}")

    async def get_execution_steps(self, history_id: int) -> List[Dict[str, Any]]:
        """获取任务执行步骤详情"""
        try:
            steps = self.db.query(ExecutionStep).filter(
                ExecutionStep.history_id == history_id
            ).order_by(ExecutionStep.step_index).all()

            return [
                {
                    "step_index": step.step_index,
                    "action_type": step.action_type,
                    "description": step.description,
                    "screenshot_path": step.screenshot_path,
                    "element_info": step.element_info,
                    "ai_response": step.ai_response,
                    "success": step.success,
                    "error_message": step.error_message,
                    "timestamp": step.timestamp.isoformat() if step.timestamp else None
                }
                for step in steps
            ]

        except Exception as e:
            raise Exception(f"获取执行步骤失败: {str(e)}")

    async def delete_task_history(self, history_id: int) -> Dict[str, Any]:
        """删除任务历史记录"""
        try:
            history = self.db.query(TaskHistory).filter(TaskHistory.id == history_id).first()
            if not history:
                raise Exception("历史记录不存在")

            # 删除相关的执行步骤
            self.db.query(ExecutionStep).filter(ExecutionStep.history_id == history_id).delete()

            # 删除历史记录
            self.db.delete(history)
            self.db.commit()

            return {
                "success": True,
                "message": "历史记录已删除"
            }

        except Exception as e:
            self.db.rollback()
            raise Exception(f"删除历史记录失败: {str(e)}")

    async def stop_current_execution(self) -> Dict[str, Any]:
        """停止当前正在执行的任务"""
        try:
            if not self.current_execution:
                return {
                    "success": False,
                    "message": "当前没有正在执行的任务"
                }

            history_id = self.current_execution.get("history_id")
            history = self.db.query(TaskHistory).filter(TaskHistory.id == history_id).first()

            if history:
                history.status = "stopped"
                history.completed_at = datetime.now()
                history.execution_time = time.time() - self.current_execution.get("start_time", 0)
                self.db.commit()

            # 清除当前执行状态
            self.current_execution = None

            return {
                "success": True,
                "message": "任务已停止"
            }

        except Exception as e:
            raise Exception(f"停止任务失败: {str(e)}")

    async def get_execution_status(self) -> Dict[str, Any]:
        """获取当前执行状态"""
        try:
            if not self.current_execution:
                return {
                    "is_running": False,
                    "message": "当前没有正在执行的任务"
                }

            history_id = self.current_execution.get("history_id")
            start_time = self.current_execution.get("start_time", 0)

            # 获取已执行的步骤数
            step_count = self.db.query(ExecutionStep).filter(
                ExecutionStep.history_id == history_id
            ).count()

            return {
                "is_running": True,
                "history_id": history_id,
                "running_time": time.time() - start_time,
                "completed_steps": step_count,
                "status": self.current_execution.get("status", "running")
            }

        except Exception as e:
            return {
                "is_running": False,
                "error": f"获取执行状态失败: {str(e)}"
            }

    async def create_task(
        self,
        title: str,
        description: str,
        automation_type: str,
        input_type: str,
        input_content: str,
        ai_model: Optional[str] = None,
        api_base: Optional[str] = None,
        temperature: Optional[float] = 0.7
    ) -> Dict[str, Any]:
        """创建新任务"""
        try:
            task = Task(
                title=title,
                description=description,
                automation_type=automation_type,
                input_type=input_type,
                input_content=input_content,
                ai_model=ai_model or settings.DEFAULT_AI_MODEL,
                api_base=api_base or settings.DEFAULT_API_BASE,
                temperature=temperature,
                status="pending"
            )
            
            self.db.add(task)
            self.db.commit()
            self.db.refresh(task)
            
            # 记录日志
            await self._add_task_log(
                task.id,
                "info",
                f"任务创建成功: {title}",
                {"automation_type": automation_type, "input_type": input_type}
            )
            
            return {
                "id": task.id,
                "title": task.title,
                "description": task.description,
                "automation_type": task.automation_type,
                "status": task.status,
                "progress": task.progress,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None
            }
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"创建任务失败: {str(e)}")
    
    async def execute_task(self, task_id: int, max_scroll_times: int = 3) -> Dict[str, Any]:
        """执行任务"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise Exception("任务不存在")
            
            # 更新任务状态
            task.status = "running"
            task.started_at = datetime.utcnow()
            task.progress = 0.0
            self.db.commit()
            
            # 记录开始执行日志
            await self._add_task_log(
                task_id,
                "info",
                "开始执行任务",
                {"max_scroll_times": max_scroll_times}
            )
            
            # 这里应该调用AI服务执行任务
            # 为了演示，暂时返回成功状态
            
            # 模拟执行过程
            import asyncio
            await asyncio.sleep(1)  # 模拟执行时间
            
            # 更新任务状态
            task.status = "completed"
            task.completed_at = datetime.utcnow()
            task.progress = 100.0
            task.result = {
                "success": True,
                "message": "任务执行完成",
                "steps_completed": 5,
                "execution_time": 30.5
            }
            self.db.commit()
            
            # 记录完成日志
            await self._add_task_log(
                task_id,
                "info",
                "任务执行完成",
                task.result
            )
            
            return {
                "success": True,
                "message": "任务执行完成",
                "task_id": task_id,
                "status": task.status,
                "progress": task.progress,
                "result": task.result
            }
            
        except Exception as e:
            # 更新任务状态为失败
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if task:
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = datetime.utcnow()
                self.db.commit()
                
                # 记录错误日志
                await self._add_task_log(
                    task_id,
                    "error",
                    f"任务执行失败: {str(e)}",
                    {"error": str(e)}
                )
            
            return {
                "success": False,
                "message": f"任务执行失败: {str(e)}",
                "task_id": task_id
            }
    
    async def get_tasks(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        automation_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取任务列表"""
        try:
            query = self.db.query(Task)
            
            if status:
                query = query.filter(Task.status == status)
            
            if automation_type:
                query = query.filter(Task.automation_type == automation_type)
            
            tasks = query.offset(skip).limit(limit).all()
            
            return [
                {
                    "id": task.id,
                    "title": task.title,
                    "description": task.description,
                    "automation_type": task.automation_type,
                    "status": task.status,
                    "progress": task.progress,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None
                }
                for task in tasks
            ]
            
        except Exception as e:
            raise Exception(f"获取任务列表失败: {str(e)}")
    
    async def get_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取任务详情"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return None
            
            return {
                "id": task.id,
                "title": task.title,
                "description": task.description,
                "automation_type": task.automation_type,
                "input_type": task.input_type,
                "input_content": task.input_content,
                "ai_model": task.ai_model,
                "api_base": task.api_base,
                "temperature": task.temperature,
                "status": task.status,
                "progress": task.progress,
                "result": task.result,
                "error_message": task.error_message,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None
            }
            
        except Exception as e:
            raise Exception(f"获取任务详情失败: {str(e)}")
    
    async def delete_task(self, task_id: int) -> Dict[str, Any]:
        """删除任务"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise Exception("任务不存在")
            
            # 删除相关的步骤和日志
            self.db.query(TaskStep).filter(TaskStep.task_id == task_id).delete()
            self.db.query(TaskLog).filter(TaskLog.task_id == task_id).delete()
            
            # 删除任务
            self.db.delete(task)
            self.db.commit()
            
            return {
                "success": True,
                "message": f"任务 {task.title} 已删除"
            }
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"删除任务失败: {str(e)}")
    
    async def get_task_steps(self, task_id: int) -> List[Dict[str, Any]]:
        """获取任务步骤"""
        try:
            steps = self.db.query(TaskStep).filter(
                TaskStep.task_id == task_id
            ).order_by(TaskStep.step_index).all()
            
            return [
                {
                    "id": step.id,
                    "task_id": step.task_id,
                    "step_index": step.step_index,
                    "action": step.action,
                    "element_info": step.element_info,
                    "code_info": step.code_info,
                    "status": step.status,
                    "result": step.result,
                    "screenshot_path": step.screenshot_path,
                    "observations": step.observations,
                    "created_at": step.created_at.isoformat() if step.created_at else None,
                    "executed_at": step.executed_at.isoformat() if step.executed_at else None
                }
                for step in steps
            ]
            
        except Exception as e:
            raise Exception(f"获取任务步骤失败: {str(e)}")
    
    async def get_task_logs(self, task_id: int) -> List[Dict[str, Any]]:
        """获取任务日志"""
        try:
            logs = self.db.query(TaskLog).filter(
                TaskLog.task_id == task_id
            ).order_by(TaskLog.created_at.desc()).all()
            
            return [
                {
                    "id": log.id,
                    "task_id": log.task_id,
                    "step_id": log.step_id,
                    "level": log.level,
                    "message": log.message,
                    "details": log.details,
                    "created_at": log.created_at.isoformat() if log.created_at else None
                }
                for log in logs
            ]
            
        except Exception as e:
            raise Exception(f"获取任务日志失败: {str(e)}")
    
    async def stop_task(self, task_id: int) -> Dict[str, Any]:
        """停止任务执行"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise Exception("任务不存在")
            
            if task.status != "running":
                return {
                    "success": False,
                    "message": "任务未在运行中"
                }
            
            # 更新任务状态
            task.status = "stopped"
            task.completed_at = datetime.utcnow()
            self.db.commit()
            
            # 记录停止日志
            await self._add_task_log(
                task_id,
                "warning",
                "任务被手动停止",
                {"stopped_by": "user"}
            )
            
            return {
                "success": True,
                "message": "任务已停止"
            }
            
        except Exception as e:
            raise Exception(f"停止任务失败: {str(e)}")
    
    async def _add_task_log(
        self,
        task_id: int,
        level: str,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        step_id: Optional[int] = None
    ):
        """添加任务日志"""
        try:
            log = TaskLog(
                task_id=task_id,
                step_id=step_id,
                level=level,
                message=message,
                details=details
            )
            
            self.db.add(log)
            self.db.commit()
            
        except Exception as e:
            print(f"添加任务日志失败: {e}")
            self.db.rollback()
