<template>
  <div class="debug-operations">
    <div class="card">
      <div class="card-header">
        <h3>调试操作</h3>
      </div>
      <div class="card-content">
        <form @submit.prevent="executeOperation" class="operation-form">
          <!-- 操作类型选择 -->
          <div class="form-group">
            <label for="operation">操作类型:</label>
            <select v-model="operation" id="operation" required>
              <option value="">请选择操作</option>
              <option value="click">点击</option>
              <option value="input">输入</option>
              <option value="scroll">滑动/滚动</option>
            </select>
          </div>

          <!-- 位置输入 -->
          <div class="form-group">
            <label>位置选择:</label>
            <div class="position-options">
              <label class="radio-option">
                <input type="radio" v-model="positionType" value="index" />
                <span>序列索引</span>
              </label>
              <label class="radio-option">
                <input type="radio" v-model="positionType" value="coordinate" />
                <span>坐标</span>
              </label>
            </div>
          </div>

          <!-- 序列索引输入 -->
          <div v-if="positionType === 'index'" class="form-group">
            <label for="seqIndex">序列索引:</label>
            <input 
              type="number" 
              v-model.number="seqIndex" 
              id="seqIndex" 
              placeholder="输入元素序列索引"
              min="0"
            />
          </div>

          <!-- 坐标输入 -->
          <div v-if="positionType === 'coordinate'" class="coordinate-group">
            <div class="form-group">
              <label for="x">X坐标:</label>
              <input 
                type="number" 
                v-model.number="x" 
                id="x" 
                placeholder="X坐标"
                min="0"
              />
            </div>
            <div class="form-group">
              <label for="y">Y坐标:</label>
              <input 
                type="number" 
                v-model.number="y" 
                id="y" 
                placeholder="Y坐标"
                min="0"
              />
            </div>
          </div>

          <!-- 文本输入（仅输入操作时显示） -->
          <div v-if="operation === 'input'" class="form-group">
            <label for="text">输入文本:</label>
            <input 
              type="text" 
              v-model="text" 
              id="text" 
              placeholder="要输入的文本"
              required
            />
          </div>

          <!-- 滑动方向（仅滑动操作时显示） -->
          <div v-if="operation === 'scroll'" class="form-group">
            <label for="direction">滑动方向:</label>
            <select v-model="direction" id="direction" required>
              <option value="">请选择方向</option>
              <option value="up">向上</option>
              <option value="down">向下</option>
              <option value="left">向左</option>
              <option value="right">向右</option>
            </select>
          </div>

          <!-- 执行按钮 -->
          <div class="form-group">
            <button type="submit" :disabled="!canExecute || loading" class="execute-btn">
              {{ loading ? '执行中...' : '执行操作' }}
            </button>
          </div>
        </form>

        <!-- 结果显示 -->
        <div v-if="result" class="result-section">
          <h4>执行结果:</h4>
          <div :class="['result-message', result.success ? 'success' : 'error']">
            {{ result.message }}
          </div>
          <div v-if="result.success && result.action" class="result-details">
            <p><strong>操作类型:</strong> {{ result.action }}</p>
            <p v-if="result.coordinates">
              <strong>坐标:</strong> ({{ result.coordinates.x }}, {{ result.coordinates.y }})
            </p>
            <p v-if="result.text">
              <strong>输入文本:</strong> {{ result.text }}
            </p>
            <p v-if="result.direction">
              <strong>滑动方向:</strong> {{ result.direction }}
            </p>
            <p v-if="result.seq_index !== undefined">
              <strong>序列索引:</strong> {{ result.seq_index }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DebugOperations',
  props: {
    platform: {
      type: String,
      required: true,
      validator: value => ['web', 'android'].includes(value)
    }
  },
  data() {
    return {
      operation: '',
      positionType: 'index',
      seqIndex: null,
      x: null,
      y: null,
      text: '',
      direction: '',
      loading: false,
      result: null
    }
  },
  computed: {
    canExecute() {
      if (!this.operation) return false
      
      // 检查位置参数
      if (this.positionType === 'index' && (this.seqIndex === null || this.seqIndex === '')) {
        if (this.operation !== 'scroll') return false
      }
      
      if (this.positionType === 'coordinate' && (this.x === null || this.y === null || this.x === '' || this.y === '')) {
        if (this.operation !== 'scroll') return false
      }
      
      // 检查特定操作的参数
      if (this.operation === 'input' && !this.text.trim()) return false
      if (this.operation === 'scroll' && !this.direction) return false
      
      return true
    }
  },
  methods: {
    async executeOperation() {
      this.loading = true
      this.result = null
      
      try {
        const payload = {
          operation: this.operation
        }
        
        // 添加位置参数
        if (this.operation !== 'scroll') {
          if (this.positionType === 'index') {
            payload.seq_index = this.seqIndex
          } else {
            payload.x = this.x
            payload.y = this.y
          }
        }
        
        // 添加特定操作的参数
        if (this.operation === 'input') {
          payload.text = this.text
        }
        
        if (this.operation === 'scroll') {
          payload.direction = this.direction
        }
        
        // 根据平台调用不同的API
        const endpoint = this.platform === 'web' 
          ? '/api/web/debug_operation'
          : '/api/android/debug_operation'
        
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(payload)
        })
        
        this.result = await response.json()
        
      } catch (error) {
        this.result = {
          success: false,
          message: `执行失败: ${error.message}`
        }
      } finally {
        this.loading = false
      }
    },
    
    resetForm() {
      this.operation = ''
      this.positionType = 'index'
      this.seqIndex = null
      this.x = null
      this.y = null
      this.text = ''
      this.direction = ''
      this.result = null
    }
  }
}
</script>

<style scoped>
.debug-operations {
  width: 100%;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 500px;
  display: flex;
  flex-direction: column;
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.card-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.operation-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.position-options {
  display: flex;
  gap: 16px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  cursor: pointer;
}

.coordinate-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.execute-btn {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.execute-btn:hover:not(:disabled) {
  background: #2563eb;
}

.execute-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.result-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.result-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #374151;
}

.result-message {
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 12px;
}

.result-message.success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.result-message.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.result-details {
  font-size: 13px;
  color: #6b7280;
}

.result-details p {
  margin: 4px 0;
}
</style>
