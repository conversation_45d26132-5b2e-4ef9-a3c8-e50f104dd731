"""
设备相关数据模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON
from sqlalchemy.sql import func

from app.core.database import Base

class AndroidDevice(Base):
    """Android设备模型"""
    __tablename__ = "android_devices"
    
    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String(100), unique=True, nullable=False, comment="设备ID")
    device_name = Column(String(200), comment="设备名称")
    
    # 设备信息
    brand = Column(String(50), comment="品牌")
    model = Column(String(100), comment="型号")
    android_version = Column(String(20), comment="Android版本")
    screen_resolution = Column(String(20), comment="屏幕分辨率")
    
    # 连接状态
    is_connected = Column(Boolean, default=False, comment="是否连接")
    is_active = Column(Boolean, default=False, comment="是否为当前活动设备")
    
    # 配置信息
    adb_port = Column(Integer, comment="ADB端口")
    poco_port = Column(Integer, comment="Poco端口")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_connected_at = Column(DateTime(timezone=True), comment="最后连接时间")

class WebBrowser(Base):
    """Web浏览器实例模型"""
    __tablename__ = "web_browsers"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(100), unique=True, nullable=False, comment="会话ID")
    
    # 浏览器配置
    url = Column(String(500), comment="当前URL")
    cookies = Column(JSON, comment="Cookie配置")
    headers = Column(JSON, comment="Header配置")
    is_h5_mode = Column(Boolean, default=False, comment="是否H5模式")
    
    # 状态信息
    is_active = Column(Boolean, default=False, comment="是否活跃")
    viewport_width = Column(Integer, comment="视口宽度")
    viewport_height = Column(Integer, comment="视口高度")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_accessed_at = Column(DateTime(timezone=True), comment="最后访问时间")

class AppPackage(Base):
    """应用包模型"""
    __tablename__ = "app_packages"

    id = Column(Integer, primary_key=True, index=True)
    package_name = Column(String(200), comment="包名")
    app_name = Column(String(200), comment="应用名称")
    version = Column(String(50), comment="版本号")

    # 下载信息
    download_url = Column(String(500), comment="下载URL")
    file_path = Column(String(500), comment="本地文件路径")
    file_size = Column(Integer, comment="文件大小")

    # 安装状态
    is_downloaded = Column(Boolean, default=False, comment="是否已下载")
    is_installed = Column(Boolean, default=False, comment="是否已安装")
    device_id = Column(String(100), comment="安装的设备ID")

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    installed_at = Column(DateTime(timezone=True), comment="安装时间")

class ApkInstallTask(Base):
    """APK安装任务模型"""
    __tablename__ = "apk_install_tasks"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), unique=True, nullable=False, comment="任务ID")

    # 任务信息
    download_url = Column(String(500), nullable=False, comment="下载URL")
    device_id = Column(String(100), nullable=False, comment="目标设备ID")
    filename = Column(String(200), comment="文件名")

    # 状态信息
    status = Column(String(20), default="pending", comment="任务状态: pending, downloading, installing, completed, failed")
    progress = Column(Integer, default=0, comment="进度百分比 0-100")

    # 下载信息
    total_size = Column(Integer, comment="文件总大小")
    downloaded_size = Column(Integer, default=0, comment="已下载大小")
    download_speed = Column(String(20), comment="下载速度")

    # 结果信息
    file_path = Column(String(500), comment="本地文件路径")
    error_message = Column(Text, comment="错误信息")

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), comment="完成时间")
