"""
Web端服务
"""

import asyncio
import json
import time
import base64
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from pathlib import Path
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

from app.models.device import WebBrowser
from app.models.report import Screenshot, DomSnapshot
from app.core.config import settings

class WebService:
    """Web端服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.session_id = None
        
    async def _ensure_browser(self):
        """确保浏览器实例存在"""
        if not self.playwright:
            self.playwright = await async_playwright().start()
            
        if not self.browser:
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 显示浏览器窗口
                args=['--no-sandbox', '--disable-setuid-sandbox']
            )
            
        if not self.context:
            self.context = await self.browser.new_context()
            
        if not self.page:
            self.page = await self.context.new_page()
    
    async def navigate(
        self,
        url: str,
        cookies: Optional[List[Dict[str, Any]]] = None,
        headers: Optional[Dict[str, str]] = None,
        is_h5_mode: bool = False,
        viewport_width: Optional[int] = None,
        viewport_height: Optional[int] = None
    ) -> Dict[str, Any]:
        """导航到指定URL"""
        try:
            await self._ensure_browser()
            
            # 设置视口大小
            if viewport_width and viewport_height:
                # 使用用户指定的窗口大小
                width, height = viewport_width, viewport_height
            elif is_h5_mode:
                # H5模式默认大小
                width, height = 375, 667
            else:
                # 桌面模式默认大小
                width, height = 1920, 1080

            await self.page.set_viewport_size({"width": width, "height": height})
            
            # 设置Headers
            if headers:
                await self.page.set_extra_http_headers(headers)
            
            # 设置Cookies
            if cookies:
                await self.context.add_cookies(cookies)
            
            # 导航到URL
            await self.page.goto(url, wait_until="load", timeout=30000)
            
            # 生成会话ID
            self.session_id = f"web_session_{int(time.time())}"
            
            # 保存到数据库
            web_browser = WebBrowser(
                session_id=self.session_id,
                url=url,
                cookies=cookies,
                headers=headers,
                is_h5_mode=is_h5_mode,
                is_active=True,
                viewport_width=width,
                viewport_height=height
            )
            
            # 清除其他活跃会话
            self.db.query(WebBrowser).update({"is_active": False})
            self.db.add(web_browser)
            self.db.commit()
            
            return {
                "success": True,
                "message": f"成功导航到 {url}",
                "session_id": self.session_id,
                "current_url": self.page.url,
                "title": await self.page.title()
            }
            
        except Exception as e:
            return {"success": False, "message": f"导航失败: {str(e)}"}
    
    async def set_cookies(self, cookies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """设置Cookie"""
        try:
            await self._ensure_browser()
            await self.context.add_cookies(cookies)
            
            return {
                "success": True,
                "message": "Cookie设置成功",
                "cookies_count": len(cookies)
            }
            
        except Exception as e:
            return {"success": False, "message": f"设置Cookie失败: {str(e)}"}
    
    async def set_headers(self, headers: Dict[str, str]) -> Dict[str, Any]:
        """设置Header"""
        try:
            await self._ensure_browser()
            await self.page.set_extra_http_headers(headers)
            
            return {
                "success": True,
                "message": "Header设置成功",
                "headers": headers
            }
            
        except Exception as e:
            return {"success": False, "message": f"设置Header失败: {str(e)}"}
    
    async def execute_javascript(self, code: str) -> Dict[str, Any]:
        """执行JavaScript代码"""
        try:
            # 检查是否有活跃的浏览器实例
            if not self.page:
                return {
                    "success": False,
                    "message": "没有活跃的浏览器页面，请先导航到目标页面"
                }

            # 检查页面是否仍然有效
            try:
                await self.page.title()  # 测试页面是否仍然可访问
            except Exception:
                return {
                    "success": False,
                    "message": "当前页面已失效，请重新导航到目标页面"
                }

            result = await self.page.evaluate(code)

            return {
                "success": True,
                "message": "JavaScript执行成功",
                "result": result
            }

        except Exception as e:
            return {"success": False, "message": f"JavaScript执行失败: {str(e)}"}
    
    async def reset_browser(self) -> Dict[str, Any]:
        """重置浏览器实例"""
        try:
            # 关闭当前页面和上下文
            if self.page:
                try:
                    await self.page.close()
                except Exception as e:
                    print(f"关闭页面失败: {e}")
                self.page = None

            if self.context:
                try:
                    await self.context.close()
                except Exception as e:
                    print(f"关闭上下文失败: {e}")
                self.context = None

            if self.browser:
                try:
                    await self.browser.close()
                except Exception as e:
                    print(f"关闭浏览器失败: {e}")
                self.browser = None

            if self.playwright:
                try:
                    await self.playwright.stop()
                except Exception as e:
                    print(f"停止playwright失败: {e}")
                self.playwright = None

            # 更新数据库状态 - 将所有活跃会话设为非活跃
            try:
                self.db.query(WebBrowser).update({"is_active": False})
                self.db.commit()
            except Exception as e:
                print(f"更新数据库状态失败: {e}")
                self.db.rollback()

            self.session_id = None

            return {
                "success": True,
                "message": "浏览器重置成功，所有实例已关闭"
            }

        except Exception as e:
            return {"success": False, "message": f"浏览器重置失败: {str(e)}"}
    
    async def take_screenshot(self) -> Dict[str, Any]:
        """获取页面截图"""
        try:
            # 检查是否有活跃的浏览器页面
            if not self.page:
                return {
                    "success": False,
                    "message": "没有活跃的浏览器页面，请先导航到目标页面"
                }

            # 检查页面是否仍然有效
            try:
                await self.page.title()  # 测试页面是否仍然可访问
            except Exception:
                return {
                    "success": False,
                    "message": "当前页面已失效，请重新导航到目标页面"
                }

            # 生成截图文件名
            timestamp = int(time.time())
            filename = f"web_screenshot_{timestamp}.png"
            file_path = settings.SCREENSHOTS_DIR / filename

            # 截图
            await self.page.screenshot(path=str(file_path), full_page=False)

            return {
                "success": True,
                "file_path": str(file_path),
                "url": f"/output/screenshots/{filename}",
                "timestamp": timestamp
            }

        except Exception as e:
            return {"success": False, "message": f"获取截图失败: {str(e)}"}
    
    async def get_browser_status(self) -> Dict[str, Any]:
        """获取浏览器状态"""
        try:
            if not self.page:
                return {
                    "success": True,
                    "status": "not_initialized",
                    "message": "浏览器未初始化"
                }
            
            current_url = self.page.url
            title = await self.page.title()
            viewport = self.page.viewport_size
            
            return {
                "success": True,
                "status": "active",
                "current_url": current_url,
                "title": title,
                "viewport": viewport,
                "session_id": self.session_id
            }
            
        except Exception as e:
            return {"success": False, "message": f"获取浏览器状态失败: {str(e)}"}
    
    async def get_current_url(self) -> Dict[str, Any]:
        """获取当前页面URL"""
        try:
            await self._ensure_browser()
            
            current_url = self.page.url
            title = await self.page.title()
            
            return {
                "success": True,
                "url": current_url,
                "title": title
            }
            
        except Exception as e:
            return {"success": False, "message": f"获取当前URL失败: {str(e)}"}
    
    async def get_dom_tree_and_page_screenshot(self, ai_exec: bool = False) -> Dict[str, Any]:
        """获取DOM树和页面截图（用于AI执行）"""
        try:
            await self._ensure_browser()
            
            # 获取视口信息
            viewport = self.page.viewport_size
            
            # 获取页面最大高度，判断是否可以滚动
            max_height = await self.page.evaluate("document.body.scrollHeight")
            is_scrollable = max_height > viewport["height"]
            
            # 截图
            screenshot_bytes = await self.page.screenshot(full_page=False, scale="css")
            
            # 获取DOM树
            dom_tree = await self._get_dom_tree()
            
            # 绘制边界框
            annotated_image = self._draw_bounding_boxes(screenshot_bytes, dom_tree)
            
            # 保存标注后的图像
            timestamp = int(time.time())
            filename = f"web_annotated_{timestamp}.png"
            file_path = settings.SCREENSHOTS_DIR / filename
            
            annotated_image.save(file_path)
            
            # 编码为base64
            base64_image = self._encode_image_to_base64(str(file_path))
            
            return {
                "base64_image": base64_image,
                "viewport": viewport,
                "is_scrollable": is_scrollable,
                "dom_tree": dom_tree,
                "screenshot_path": str(file_path)
            }
            
        except Exception as e:
            print(f"获取DOM树和页面截图失败: {e}")
            return {"error": str(e)}
    
    async def _get_dom_tree(self) -> Dict[str, Any]:
        """获取DOM树结构"""
        # 完整实现DOM树获取逻辑，参考需求文档中的JavaScript代码
        dom_tree = await self.page.evaluate("""() => {
            const isVisibleElement = (node) => {
                if (node.nodeType !== 1) return false;
                const style = window.getComputedStyle(node);
                const rect = node.getBoundingClientRect();
                return true;
                return style.display !== 'none' &&
                       style.visibility !== 'hidden' &&
                       style.opacity !== '0' &&
                       rect.width > 0 &&
                       rect.height > 0;
            };

            const isInteractiveElement = (node) => {
                if (node.nodeType !== 1) return false;
                const interactiveTags = ['a', 'button', 'input', 'select', 'textarea'];
                return interactiveTags.includes(node.tagName.toLowerCase()) ||
                       node.onclick != null ||
                       node.getAttribute('role') === 'button';
            };

            const shouldSkipElement = (node) => {
                if (node.nodeType !== 1) return false;
                const skipTags = ['script', 'style', 'noscript', 'iframe', 'meta', 'link', 'head'];
                return skipTags.includes(node.tagName.toLowerCase());
            };

            const processNode = (node, index = 0) => {
                if (node.nodeType === 8 || shouldSkipElement(node)) return null;

                if (node.nodeType === 3 && !node.textContent.trim()) {
                    return null;
                }

                const result = {
                    index: index,
                    tag: node.nodeType === 1 ? node.tagName.toLowerCase() : '#text',
                    type: node.nodeType === 3 ? 'text' : 'element'
                };

                if (node.nodeType === 3) {
                    const text = node.textContent.trim();
                    if (text) {
                        let cleanedText = text.replace(/[\\u{E000}-\\u{F8FF}]|[\\u{1F000}-\\u{1FFFF}]|[\\u{2000}-\\u{2FFF}]|[\\u{F0000}-\\u{FFFFF}]/gu, '');
                        cleanedText = cleanedText.replace(/\\udbf0\\udc6e/g, '');
                        cleanedText = cleanedText.replace(/[^\\x20-\\x7E\\u4E00-\\u9FFF\\u3000-\\u303F\\uFF00-\\uFFEF\\s]/g, '');
                        result.payload = { text: cleanedText };
                    }
                    return result;
                }

                if (node.nodeType === 1) {
                    const isVisible = isVisibleElement(node);
                    result.payload = {};

                    const text = node.textContent.trim();
                    if (text && text.length <= 20) {
                        let cleanedText = text.replace(/[\\u{E000}-\\u{F8FF}]|[\\u{1F000}-\\u{1FFFF}]|[\\u{2000}-\\u{2FFF}]|[\\u{F0000}-\\u{FFFFF}]/gu, '');
                        cleanedText = cleanedText.replace(/\\udbf0\\udc6e/g, '');
                        cleanedText = cleanedText.replace(/[^\\x20-\\x7E\\u4E00-\\u9FFF\\u3000-\\u303F\\uFF00-\\uFFEF\\s]/g, '');
                        result.payload.text = cleanedText;
                    }

                    const importantAttrs = ['id', 'class', 'name', 'type', 'role', 'aria-label', 'placeholder', 'href', 'src', 'value', 'title', 'for'];
                    for (let attr of node.attributes) {
                        if (attr.name === 'data-exposure') {
                            try {
                                result.payload['data-exposure'] = JSON.parse(attr.value).ubtKey;
                            } catch (e) {
                                result.payload['data-exposure'] = attr.value;
                            }
                        }
                        if (attr.name === 'testid') {
                            try {
                                if (attr.value.includes('referConfig') && attr.value.includes('oid')) {
                                    result.payload.testid = JSON.parse(attr.value).referConfig.oid;
                                } else if (attr.value.includes('viewID')) {
                                    result.payload.testid = JSON.parse(attr.value).viewID;
                                } else {
                                    result.payload.testid = attr.value;
                                }
                            } catch (e) {
                                result.payload.testid = attr.value;
                            }
                        }
                        if (attr.name === 'page-module') {
                            try {
                                result.payload['page-module'] = JSON.parse(attr.value).moduleId;
                            } catch (e) {
                                result.payload['page-module'] = attr.value;
                            }
                        }
                        if (attr.name === 'data-testid') {
                            try {
                                result.payload['data-testid'] = JSON.parse(attr.value).referConfig.oid;
                            } catch (e) {
                                result.payload['data-testid'] = attr.value;
                            }
                        }
                        if (importantAttrs.includes(attr.name) && attr.value) {
                            if (attr.value.length <= 50) {
                                result.payload[attr.name] = attr.value;
                            }
                        }
                    }

                    if (true) {
                        const rect = node.getBoundingClientRect();
                        result.payload.rect = {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        };
                    }

                    result.payload.visible = true;

                    if (node.childNodes && node.childNodes.length > 0) {
                        const children = [];
                        let childIndex = 0;
                        for (const childNode of node.childNodes) {
                            const childResult = processNode(childNode, childIndex);
                            if (childResult) {
                                children.push(childResult);
                                childIndex++;
                            }
                        }
                        if (children.length > 0) {
                            result.children = children;
                        }
                    }
                }

                return result;
            };

            return processNode(document.body);
        }""")

        # 添加全局顺序索引
        self._add_sequential_index(dom_tree)

        return dom_tree
    
    def _add_sequential_index(self, node: Dict[str, Any], index_map: Optional[Dict[str, int]] = None):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}
        
        node["seq_index"] = index_map["current"]
        index_map["current"] += 1
        
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)
    
    def _draw_bounding_boxes(self, screenshot_bytes: bytes, dom_tree: Dict[str, Any]):
        """在截图上绘制边界框"""
        from PIL import Image, ImageDraw, ImageFont
        from io import BytesIO
        import random
        import colorsys

        # 将截图字节转换为PIL图像
        img = Image.open(BytesIO(screenshot_bytes))
        draw = ImageDraw.Draw(img, 'RGBA')  # 使用RGBA模式以支持透明度

        # 获取页面大小
        width, height = img.size

        # 尝试加载字体
        try:
            font = ImageFont.truetype("Arial", 14)
        except:
            font = ImageFont.load_default()

        # 重置随机种子，确保颜色生成的一致性
        random.seed(42)

        # 递归绘制边界框
        self._draw_node_bounding_box(draw, dom_tree, width, height, font)

        return img

    def _draw_node_bounding_box(self, draw, node, viewport_width, viewport_height, font):
        """递归地为每个节点绘制边界框"""
        if "payload" in node and "rect" in node["payload"]:
            rect = node["payload"]["rect"]

            # 使用rect中的坐标和大小信息
            x1 = rect["x"]
            y1 = rect["y"]
            x2 = x1 + rect["width"]
            y2 = y1 + rect["height"]

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框 - 使用随机颜色，增加线条宽度以提高可见性
            draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)

            # 构建标签文本 - 包含seq_index
            label_text = str(seq_index)

            # 在边界框顶部绘制标签
            if label_text:
                try:
                    # 限制标签文本总长度
                    if len(label_text) > 25:
                        label_text = label_text[:22] + "..."

                    # 使用字体计算实际文本大小
                    try:
                        bbox = draw.textbbox((0, 0), label_text, font=font)
                        text_width = bbox[2] - bbox[0] + 6  # 添加padding
                        text_height = bbox[3] - bbox[1] + 4  # 添加padding
                    except:
                        # 如果textbbox不可用，使用估算
                        text_width = len(label_text) * 8 + 6
                        text_height = 16

                    # 确保标签不会超出屏幕边界
                    label_x = x1
                    if label_x + text_width > viewport_width:
                        label_x = viewport_width - text_width
                    if label_x < 0:
                        label_x = 0

                    # 确保标签不会超出屏幕顶部
                    label_y = y1 - text_height
                    if label_y < 0:
                        label_y = y2  # 如果顶部放不下，放到底部
                        if label_y + text_height > viewport_height:
                            label_y = viewport_height - text_height

                    # 创建标签背景颜色（使用边界框颜色的半透明版本）
                    bg_color = border_color + (200,)  # 添加alpha通道

                    # 绘制标签背景
                    draw.rectangle(
                        [label_x, label_y, label_x + text_width, label_y + text_height],
                        fill=bg_color
                    )

                    # 获取对比度高的文本颜色
                    text_color = self._get_contrasting_text_color(border_color)

                    # 绘制文本
                    draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)

                except Exception as e:
                    print(f"绘制标签失败: {str(e)}")

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_node_bounding_box(draw, child, viewport_width, viewport_height, font)

    def _generate_random_color(self, seq_index):
        """根据seq_index生成随机但一致的颜色"""
        import random
        import colorsys

        # 使用seq_index作为种子，确保相同的seq_index总是生成相同的颜色
        random.seed(hash(str(seq_index)) % 2147483647)

        # 生成高饱和度、中等亮度的颜色，确保视觉效果好
        hue = random.random()  # 色相：0-1
        saturation = 0.7 + random.random() * 0.3  # 饱和度：0.7-1.0
        lightness = 0.4 + random.random() * 0.3   # 亮度：0.4-0.7

        # 转换HSL到RGB
        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        return tuple(int(c * 255) for c in rgb)

    def _get_contrasting_text_color(self, bg_color):
        """根据背景颜色获取对比度高的文本颜色"""
        # 计算背景颜色的亮度
        r, g, b = bg_color[:3]  # 只取RGB值，忽略alpha
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        # 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
        return (255, 255, 255) if brightness < 128 else (0, 0, 0)

    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    async def get_dom_element_by_index(self, seq_index: int) -> Dict[str, Any]:
        """根据序列索引查询DOM元素信息"""
        try:
            await self._ensure_browser()

            # 获取DOM树
            dom_tree = await self._get_dom_tree()

            # 递归查找指定索引的元素
            element = self._find_element_by_index(dom_tree, seq_index)

            if element:
                return {
                    "success": True,
                    "element": element,
                    "message": f"找到索引为 {seq_index} 的元素"
                }
            else:
                return {
                    "success": False,
                    "message": f"未找到索引为 {seq_index} 的元素"
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"查询DOM元素失败: {str(e)}"
            }

    def _find_element_by_index(self, node: Dict[str, Any], target_index: int) -> Optional[Dict[str, Any]]:
        """递归查找指定索引的DOM元素"""
        if node.get("seq_index") == target_index:
            return node

        # 递归查找子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                result = self._find_element_by_index(child, target_index)
                if result:
                    return result

        return None

    # Web端操作方法
    async def click_by_coordinate(self, x: int, y: int) -> Dict[str, Any]:
        """通过坐标点击"""
        try:
            await self._ensure_browser()
            await self.page.mouse.click(x, y)

            return {
                "success": True,
                "message": f"成功点击坐标 ({x}, {y})",
                "action": "click",
                "coordinates": {"x": x, "y": y}
            }
        except Exception as e:
            return {"success": False, "message": f"点击失败: {str(e)}"}

    async def click_by_seq_index(self, seq_index: int) -> Dict[str, Any]:
        """通过seq_index点击元素"""
        try:
            await self._ensure_browser()

            # 获取DOM树
            dom_tree = await self._get_dom_tree()

            # 查找指定seq_index的元素
            element_info = self._find_element_by_seq_index(dom_tree, seq_index)
            if not element_info:
                return {"success": False, "message": f"未找到seq_index为{seq_index}的元素"}

            # 获取元素中心坐标
            rect = element_info.get("payload", {}).get("rect", {})
            if not rect:
                return {"success": False, "message": "元素没有位置信息"}

            center_x = rect["x"] + rect["width"] // 2
            center_y = rect["y"] + rect["height"] // 2

            # 点击元素中心
            await self.page.mouse.click(center_x, center_y)

            return {
                "success": True,
                "message": f"成功点击seq_index为{seq_index}的元素",
                "action": "click",
                "seq_index": seq_index,
                "coordinates": {"x": center_x, "y": center_y},
                "element_info": element_info
            }
        except Exception as e:
            return {"success": False, "message": f"点击失败: {str(e)}"}

    async def input_text(self, text: str, seq_index: Optional[int] = None, x: Optional[int] = None, y: Optional[int] = None) -> Dict[str, Any]:
        """输入文本"""
        try:
            await self._ensure_browser()

            # 如果提供了seq_index，先点击该元素
            if seq_index is not None:
                click_result = await self.click_by_seq_index(seq_index)
                if not click_result["success"]:
                    return click_result
            elif x is not None and y is not None:
                # 如果提供了坐标，先点击该位置
                await self.page.mouse.click(x, y)

            # 清空当前输入框内容
            await self.page.keyboard.press("Control+a")

            # 输入文本
            await self.page.keyboard.type(text)

            return {
                "success": True,
                "message": f"成功输入文本: {text}",
                "action": "input",
                "text": text,
                "seq_index": seq_index,
                "coordinates": {"x": x, "y": y} if x and y else None
            }
        except Exception as e:
            return {"success": False, "message": f"输入文本失败: {str(e)}"}

    async def input_text_by_coordinate(self, x: int, y: int, text: str) -> Dict[str, Any]:
        """通过坐标输入文本"""
        try:
            await self._ensure_browser()

            # 先点击坐标位置
            await self.page.mouse.click(x, y)
            await asyncio.sleep(0.1)

            # 清空输入框并输入文本
            await self.page.keyboard.press("Control+A")
            await self.page.keyboard.type(text)

            return {
                "success": True,
                "message": f"成功在坐标({x}, {y})输入文本: {text}",
                "action": "input",
                "text": text,
                "coordinates": {"x": x, "y": y}
            }
        except Exception as e:
            return {"success": False, "message": f"输入文本失败: {str(e)}"}

    async def input_text_by_seq_index(self, seq_index: int, text: str) -> Dict[str, Any]:
        """通过序列索引输入文本"""
        try:
            # 获取DOM元素信息
            element_result = await self.get_dom_element_by_index(seq_index)
            if not element_result["success"]:
                return element_result

            element_info = element_result["element"]

            # 获取元素中心坐标
            rect = element_info.get("payload", {}).get("rect", {})
            if not rect:
                return {"success": False, "message": "元素没有位置信息"}

            center_x = rect["x"] + rect["width"] // 2
            center_y = rect["y"] + rect["height"] // 2

            # 使用坐标输入文本
            result = await self.input_text_by_coordinate(center_x, center_y, text)
            if result["success"]:
                result.update({
                    "seq_index": seq_index,
                    "element_info": element_info
                })

            return result

        except Exception as e:
            return {"success": False, "message": f"输入文本失败: {str(e)}"}

    async def scroll_page(self, direction: str = "down", distance: int = 500) -> Dict[str, Any]:
        """滚动页面"""
        try:
            await self._ensure_browser()

            if direction == "down":
                await self.page.mouse.wheel(0, distance)
            elif direction == "up":
                await self.page.mouse.wheel(0, -distance)
            elif direction == "left":
                await self.page.mouse.wheel(-distance, 0)
            elif direction == "right":
                await self.page.mouse.wheel(distance, 0)
            else:
                return {"success": False, "message": f"不支持的滚动方向: {direction}"}

            # 等待滚动完成
            await self.page.wait_for_timeout(500)

            return {
                "success": True,
                "message": f"成功向{direction}滚动{distance}像素",
                "action": "scroll",
                "direction": direction,
                "distance": distance
            }
        except Exception as e:
            return {"success": False, "message": f"滚动失败: {str(e)}"}

    async def press_key(self, key: str) -> Dict[str, Any]:
        """按键操作"""
        try:
            await self._ensure_browser()
            await self.page.keyboard.press(key)

            return {
                "success": True,
                "message": f"成功按下按键: {key}",
                "action": "press_key",
                "key": key
            }
        except Exception as e:
            return {"success": False, "message": f"按键操作失败: {str(e)}"}

    async def wait_for_element(self, selector: str, timeout: int = 5000) -> Dict[str, Any]:
        """等待元素出现"""
        try:
            await self._ensure_browser()
            await self.page.wait_for_selector(selector, timeout=timeout)

            return {
                "success": True,
                "message": f"元素 {selector} 已出现",
                "action": "wait_for_element",
                "selector": selector
            }
        except Exception as e:
            return {"success": False, "message": f"等待元素失败: {str(e)}"}

    def _find_element_by_seq_index(self, node: Dict[str, Any], target_seq_index: int) -> Optional[Dict[str, Any]]:
        """在DOM树中查找指定seq_index的元素"""
        if node.get("seq_index") == target_seq_index:
            return node

        if "children" in node and node["children"]:
            for child in node["children"]:
                result = self._find_element_by_seq_index(child, target_seq_index)
                if result:
                    return result

        return None

    # 实时执行需要的方法
    async def get_browser_status_for_execution(self) -> Dict[str, Any]:
        """获取浏览器状态（用于实时执行验证）"""
        try:
            if not self.page:
                return {
                    "is_ready": False,
                    "message": "浏览器未初始化，请先导航到页面"
                }

            # 检查页面是否已加载
            try:
                current_url = self.page.url
                if not current_url or current_url == "about:blank":
                    return {
                        "is_ready": False,
                        "message": "请先导航到目标页面"
                    }

                return {
                    "is_ready": True,
                    "current_url": current_url,
                    "message": f"浏览器已准备就绪，当前页面: {current_url}"
                }
            except Exception:
                return {
                    "is_ready": False,
                    "message": "浏览器连接异常"
                }

        except Exception as e:
            return {
                "is_ready": False,
                "error": f"获取浏览器状态失败: {str(e)}"
            }

    async def execute_scroll_action(self, exec_code: str) -> Dict[str, Any]:
        """执行滚动操作"""
        try:
            # 解析执行代码中的滚动参数
            # 示例代码可能是: scroll_page("down", 300)

            if "scroll_page" in exec_code:
                if "down" in exec_code:
                    direction = "down"
                elif "up" in exec_code:
                    direction = "up"
                else:
                    direction = "down"

                # 提取距离参数（如果有）
                import re
                distance_match = re.search(r'(\d+)', exec_code)
                distance = int(distance_match.group(1)) if distance_match else 500

                result = await self.scroll_page(direction, distance)
                return result
            else:
                return {
                    "success": False,
                    "error_message": "不支持的滚动操作代码"
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"执行滚动操作失败: {str(e)}"
            }

    async def execute_action(self, exec_code: str) -> Dict[str, Any]:
        """执行具体的操作动作"""
        try:
            # 解析并执行AI生成的操作代码
            # 代码可能包含点击、输入文本等操作

            if "clickByCoordinate" in exec_code or "click_by_seq_index" in exec_code:
                # 提取seq_index
                import re
                seq_match = re.search(r'(\d+)', exec_code)
                if seq_match:
                    seq_index = int(seq_match.group(1))
                    result = await self.click_by_seq_index(seq_index)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析点击操作的seq_index"
                    }

            elif "input_text" in exec_code:
                # 提取文本和seq_index
                import re
                # 匹配类似 input_text_by_coordinate(seq_index, "text")
                match = re.search(r'input_text.*?(\d+).*?"([^"]*)"', exec_code)
                if match:
                    seq_index = int(match.group(1))
                    text = match.group(2)
                    result = await self.input_text(text, seq_index=seq_index)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析输入文本操作的参数"
                    }

            elif "press_key" in exec_code:
                # 提取按键
                import re
                key_match = re.search(r'"([^"]*)"', exec_code)
                if key_match:
                    key = key_match.group(1)
                    result = await self.press_key(key)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析按键操作的参数"
                    }

            else:
                return {
                    "success": False,
                    "error_message": f"不支持的操作代码: {exec_code}"
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"执行操作失败: {str(e)}"
            }

    async def take_screenshot_for_execution(self) -> Dict[str, Any]:
        """为实时执行获取页面截图"""
        try:
            result = await self.take_screenshot()

            # 转换返回格式以符合实时执行的需求
            if result.get("success"):
                return {
                    "success": True,
                    "screenshot_path": result["file_path"],
                    "url": result["url"]
                }
            else:
                return {
                    "success": False,
                    "error_message": result.get("message", "截图失败")
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"获取截图失败: {str(e)}"
            }
