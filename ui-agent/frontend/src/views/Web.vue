<template>
  <div class="web-page">
    <div class="page-header">
      <h1 class="page-title">Web端管理</h1>
      <p class="page-description">管理Web浏览器，导航页面，执行自动化操作</p>
    </div>

    <div class="content-grid">
      <!-- 浏览器控制 -->
      <el-card class="browser-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">浏览器控制</span>
            <el-button size="small" @click="getBrowserStatus">
              <el-icon><Refresh /></el-icon>
              状态
            </el-button>
          </div>
        </template>

        <el-form :model="browserForm" label-width="80px">
          <el-form-item label="URL">
            <el-input
              v-model="browserForm.url"
              placeholder="请输入完整的网址，如：https://www.example.com"
              clearable
            />
          </el-form-item>

          <el-form-item label="模式">
            <el-radio-group v-model="browserForm.isH5Mode">
              <el-radio :label="false">桌面模式</el-radio>
              <el-radio :label="true">H5移动模式</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="窗口大小">
            <div class="viewport-settings">
              <el-row :gutter="12">
                <el-col :span="10">
                  <el-input
                    v-model.number="browserForm.viewportWidth"
                    placeholder="宽度"
                    type="number"
                    :min="320"
                    :max="3840"
                  >
                    <template #append>px</template>
                  </el-input>
                </el-col>
                <el-col :span="2" class="viewport-separator">
                  <span>×</span>
                </el-col>
                <el-col :span="10">
                  <el-input
                    v-model.number="browserForm.viewportHeight"
                    placeholder="高度"
                    type="number"
                    :min="240"
                    :max="2160"
                  >
                    <template #append>px</template>
                  </el-input>
                </el-col>
                <el-col :span="2">
                  <el-tooltip content="清空使用默认大小" placement="top">
                    <el-button
                      size="small"
                      @click="clearViewportSize"
                      :icon="Delete"
                    />
                  </el-tooltip>
                </el-col>
              </el-row>
              <div class="viewport-presets">
                <el-button-group size="small">
                  <el-button @click="setViewportPreset(1920, 1080)">1920×1080</el-button>
                  <el-button @click="setViewportPreset(1366, 768)">1366×768</el-button>
                  <el-button @click="setViewportPreset(375, 667)">iPhone SE</el-button>
                  <el-button @click="setViewportPreset(414, 896)">iPhone XR</el-button>
                </el-button-group>
              </div>
              <div class="viewport-hint">
                <el-text type="info" size="small">
                  留空将使用默认窗口大小。桌面模式建议1920×1080，移动模式建议375×667
                </el-text>
              </div>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              :loading="loading.navigate"
              @click="navigateToUrl"
              :disabled="!browserForm.url"
            >
              <el-icon v-if="!loading.navigate"><Link /></el-icon>
              {{ loading.navigate ? '导航中...' : '访问页面' }}
            </el-button>
            <el-button @click="resetBrowser" :loading="loading.reset">
              <el-icon><RefreshRight /></el-icon>
              重置浏览器
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 浏览器状态 -->
        <div v-if="browserStatus" class="browser-status">
          <el-divider>浏览器状态</el-divider>
          <div class="status-info">
            <div class="status-item">
              <span class="label">状态:</span>
              <el-tag :type="browserStatus.status === 'active' ? 'success' : 'warning'">
                {{ browserStatus.status === 'active' ? '活跃' : '未初始化' }}
              </el-tag>
            </div>
            <div v-if="browserStatus.current_url" class="status-item">
              <span class="label">当前URL:</span>
              <span class="value">{{ browserStatus.current_url }}</span>
            </div>
            <div v-if="browserStatus.title" class="status-item">
              <span class="label">页面标题:</span>
              <span class="value">{{ browserStatus.title }}</span>
            </div>
            <div v-if="browserStatus.viewport" class="status-item">
              <span class="label">视口大小:</span>
              <span class="value">{{ browserStatus.viewport.width }}x{{ browserStatus.viewport.height }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- Cookie和Header设置 -->
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">Cookie & Header</span>
          </div>
        </template>

        <el-tabs v-model="activeConfigTab">
          <el-tab-pane label="Cookie设置" name="cookies">
            <div class="config-section">
              <el-button size="small" @click="addCookie" type="primary">
                <el-icon><Plus /></el-icon>
                添加Cookie
              </el-button>
              
              <div v-if="cookies.length === 0" class="empty-tip">
                <el-text type="info">暂无Cookie配置</el-text>
              </div>
              
              <div v-else class="cookie-list">
                <div
                  v-for="(cookie, index) in cookies"
                  :key="index"
                  class="cookie-item"
                >
                  <el-input
                    v-model="cookie.name"
                    placeholder="Cookie名称"
                    size="small"
                    style="width: 120px"
                  />
                  <el-input
                    v-model="cookie.value"
                    placeholder="Cookie值"
                    size="small"
                    style="width: 200px"
                  />
                  <el-input
                    v-model="cookie.domain"
                    placeholder="域名(可选)"
                    size="small"
                    style="width: 120px"
                  />
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeCookie(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              
              <div class="config-actions">
                <el-button
                  type="primary"
                  size="small"
                  :loading="loading.cookies"
                  @click="setCookies"
                  :disabled="cookies.length === 0"
                >
                  应用Cookie
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="Header设置" name="headers">
            <div class="config-section">
              <el-button size="small" @click="addHeader" type="primary">
                <el-icon><Plus /></el-icon>
                添加Header
              </el-button>
              
              <div v-if="headers.length === 0" class="empty-tip">
                <el-text type="info">暂无Header配置</el-text>
              </div>
              
              <div v-else class="header-list">
                <div
                  v-for="(header, index) in headers"
                  :key="index"
                  class="header-item"
                >
                  <el-input
                    v-model="header.name"
                    placeholder="Header名称"
                    size="small"
                    style="width: 150px"
                  />
                  <el-input
                    v-model="header.value"
                    placeholder="Header值"
                    size="small"
                    style="width: 250px"
                  />
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeHeader(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              
              <div class="config-actions">
                <el-button
                  type="primary"
                  size="small"
                  :loading="loading.headers"
                  @click="setHeaders"
                  :disabled="headers.length === 0"
                >
                  应用Header
                </el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- JavaScript执行 -->
      <el-card class="js-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">JavaScript执行</span>
          </div>
        </template>

        <el-form :model="jsForm" label-width="80px">
          <el-form-item label="代码">
            <el-input
              v-model="jsForm.code"
              type="textarea"
              :rows="6"
              placeholder="请输入JavaScript代码"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :loading="loading.js"
              @click="executeJs"
              :disabled="!jsForm.code"
            >
              <el-icon v-if="!loading.js"><CaretRight /></el-icon>
              {{ loading.js ? '执行中...' : '执行代码' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 执行结果 -->
        <div v-if="jsResult" class="js-result">
          <el-divider>执行结果</el-divider>
          <div class="result-header">
            <el-tag :type="jsResult.success ? 'success' : 'danger'" size="small">
              {{ jsResult.success ? '成功' : '失败' }}
            </el-tag>
          </div>
          <div class="result-content">
            <pre class="result-text">{{ JSON.stringify(jsResult.result, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- DOM索引查询模块 -->
      <el-card class="dom-query-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">DOM索引查询</span>
          </div>
        </template>

        <DomIndexQuery
          type="web"
          @element-click="onDomElementClick"
        />

        <div v-if="!browserStatus || browserStatus.status !== 'active'" class="warning-tip">
          <el-alert
            title="请先导航到目标页面"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>

      <!-- 调试操作模块 -->
      <el-card class="debug-operations-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">调试操作</span>
          </div>
        </template>

        <DebugOperations platform="web" />

        <div v-if="!browserStatus || browserStatus.status !== 'active'" class="warning-tip">
          <el-alert
            title="请先导航到目标页面"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>

      <!-- 页面截图 -->
      <el-card class="screenshot-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">页面截图</span>
            <div class="header-buttons">
              <el-button
                size="small"
                @click="takeScreenshot"
                :loading="loading.screenshot"
              >
                <el-icon v-if="!loading.screenshot"><Camera /></el-icon>
                {{ loading.screenshot ? '截图中...' : '截图' }}
              </el-button>
              <el-button
                size="small"
                type="success"
                @click="takeScreenshotWithDom"
                :loading="loading.domScreenshot"
              >
                <el-icon v-if="!loading.domScreenshot"><View /></el-icon>
                {{ loading.domScreenshot ? '获取中...' : 'DOM截图' }}
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="screenshot" class="screenshot-container">
          <img :src="screenshot.url" alt="页面截图" class="screenshot-image" />
          <div class="screenshot-info">
            <span>截图时间: {{ formatTime(screenshot.timestamp) }}</span>
          </div>
        </div>

        <div v-else class="empty-container">
          <el-icon class="empty-icon"><Picture /></el-icon>
          <div class="empty-text">暂无截图</div>
          <div class="empty-description">点击截图按钮获取页面截图</div>
        </div>
      </el-card>

      <!-- 实时任务执行 -->
      <el-card class="execution-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">实时任务执行</span>
            <el-button
              v-if="executionStatus.is_running"
              size="small"
              type="danger"
              @click="stopExecution"
              :loading="loading.stop"
            >
              <el-icon v-if="!loading.stop"><Close /></el-icon>
              {{ loading.stop ? '停止中...' : '停止执行' }}
            </el-button>
          </div>
        </template>

        <div v-if="!browserStatus || browserStatus.status !== 'active'" class="warning-tip">
          <el-alert
            title="请先导航到目标页面"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>

        <div v-else>
          <!-- 任务输入 -->
          <el-form :model="taskForm" label-width="80px">
            <el-form-item label="输入方式">
              <el-radio-group v-model="taskForm.inputType">
                <el-radio value="text">文本输入</el-radio>
                <el-radio value="voice" disabled>语音输入</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="任务描述">
              <el-input
                v-model="taskForm.inputContent"
                type="textarea"
                :rows="4"
                placeholder="请描述您希望在网页上执行的任务，例如：搜索商品，填写表单，点击按钮等"
                :disabled="executionStatus.is_running"
              />
            </el-form-item>

            <el-form-item label="AI配置">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-input
                    v-model="taskForm.aiModel"
                    placeholder="AI模型 (可选)"
                    :disabled="executionStatus.is_running"
                  />
                </el-col>
                <el-col :span="12">
                  <el-input-number
                    v-model="taskForm.temperature"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    placeholder="温度参数"
                    :disabled="executionStatus.is_running"
                    style="width: 100%"
                  />
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="loading.execution"
                @click="executeTask"
                :disabled="!taskForm.inputContent || executionStatus.is_running"
                style="width: 100%"
              >
                <el-icon v-if="!loading.execution"><VideoPlay /></el-icon>
                {{ loading.execution ? '执行中...' : '开始执行任务' }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 执行状态 -->
          <div v-if="executionStatus.is_running" class="execution-status">
            <el-divider>执行状态</el-divider>
            <div class="status-info">
              <el-tag type="success" size="large">
                <el-icon><Loading /></el-icon>
                正在执行中...
              </el-tag>
              <div class="status-details">
                <p>运行时间: {{ formatDuration(executionStatus.running_time) }}</p>
                <p>已完成步骤: {{ executionStatus.completed_steps }}</p>
              </div>
            </div>
          </div>

          <!-- 执行结果 -->
          <div v-if="executionResult" class="execution-result">
            <el-divider>执行结果</el-divider>
            <div class="result-header">
              <el-tag :type="executionResult.success ? 'success' : 'danger'" size="large">
                {{ executionResult.success ? '执行成功' : '执行失败' }}
              </el-tag>
              <span class="execution-time">
                耗时: {{ formatDuration(executionResult.execution_time) }}
              </span>
            </div>
            <div class="result-message">
              {{ executionResult.message }}
            </div>
            <div v-if="!executionResult.success && executionResult.error" class="error-details">
              <h4>错误详情:</h4>
              <pre>{{ executionResult.error }}</pre>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { webApi } from '@/api/web'
import DomIndexQuery from '@/components/DomIndexQuery.vue'
import DebugOperations from '@/components/DebugOperations.vue'
import {
  Link,
  Refresh,
  RefreshRight,
  Plus,
  Delete,
  CaretRight,
  Camera,
  Picture,
  Close,
  VideoPlay,
  Loading,
  View
} from '@element-plus/icons-vue'

// 响应式数据
const loading = reactive({
  navigate: false,
  reset: false,
  cookies: false,
  headers: false,
  js: false,
  screenshot: false,
  domScreenshot: false,
  execution: false,
  stop: false
})

const browserForm = reactive({
  url: '',
  isH5Mode: false,
  viewportWidth: null as number | null,
  viewportHeight: null as number | null
})

const jsForm = reactive({
  code: ''
})

const taskForm = reactive({
  inputType: 'text' as 'text' | 'voice',
  inputContent: '',
  aiModel: '',
  temperature: 0.7
})

const activeConfigTab = ref('cookies')
const browserStatus = ref<any>(null)
const jsResult = ref<any>(null)
const screenshot = ref<any>(null)
const executionStatus = ref({
  is_running: false,
  running_time: 0,
  completed_steps: 0
})
const executionResult = ref<any>(null)

// Cookie和Header配置
const cookies = ref<Array<{name: string, value: string, domain?: string}>>([])
const headers = ref<Array<{name: string, value: string}>>([])

// 窗口大小相关方法
const setViewportPreset = (width: number, height: number) => {
  browserForm.viewportWidth = width
  browserForm.viewportHeight = height
}

const clearViewportSize = () => {
  browserForm.viewportWidth = null
  browserForm.viewportHeight = null
}

// 方法
const navigateToUrl = async () => {
  loading.navigate = true
  try {
    // 检查URL是否包含协议，如果没有则添加https://
    let fullUrl = browserForm.url
    if (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
      fullUrl = 'https://' + fullUrl
    }

    const response = await webApi.navigate({
      url: fullUrl,
      is_h5_mode: browserForm.isH5Mode,
      viewport_width: browserForm.viewportWidth || undefined,
      viewport_height: browserForm.viewportHeight || undefined,
      cookies: cookies.value.filter(c => c.name && c.value),
      headers: headers.value.reduce((acc: Record<string, string>, h) => {
        if (h.name && h.value) {
          acc[h.name] = h.value
        }
        return acc
      }, {})
    })

    if (response.success) {
      ElMessage.success(response.message)
      // 立即更新浏览器状态
      await getBrowserStatus()
      // 延迟再次更新确保状态同步
      setTimeout(async () => {
        await getBrowserStatus()
      }, 1000)
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('导航失败')
  } finally {
    loading.navigate = false
  }
}

const resetBrowser = async () => {
  loading.reset = true
  try {
    const response = await webApi.resetBrowser()
    if (response.success) {
      ElMessage.success(response.message)
      browserStatus.value = null
      screenshot.value = null
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('重置浏览器失败')
  } finally {
    loading.reset = false
  }
}

const getBrowserStatus = async () => {
  try {
    const response = await webApi.getBrowserStatus()
    browserStatus.value = response
  } catch (error) {
    console.error('获取浏览器状态失败:', error)
  }
}

const addCookie = () => {
  cookies.value.push({ name: '', value: '', domain: '' })
}

const removeCookie = (index: number) => {
  cookies.value.splice(index, 1)
}

const setCookies = async () => {
  loading.cookies = true
  try {
    const validCookies = cookies.value.filter(c => c.name && c.value)
    const response = await webApi.setCookies({ cookies: validCookies })
    
    if (response.success) {
      ElMessage.success(response.message)
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('设置Cookie失败')
  } finally {
    loading.cookies = false
  }
}

const addHeader = () => {
  headers.value.push({ name: '', value: '' })
}

const removeHeader = (index: number) => {
  headers.value.splice(index, 1)
}

const setHeaders = async () => {
  loading.headers = true
  try {
    const validHeaders = headers.value.reduce((acc: Record<string, string>, h) => {
      if (h.name && h.value) {
        acc[h.name] = h.value
      }
      return acc
    }, {})

    const response = await webApi.setHeaders({ headers: validHeaders })

    if (response.success) {
      ElMessage.success(response.message)
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('设置Header失败')
  } finally {
    loading.headers = false
  }
}

const executeJs = async () => {
  loading.js = true
  try {
    const response = await webApi.executeJs({ code: jsForm.code })
    jsResult.value = response
    
    if (response.success) {
      ElMessage.success(response.message)
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('执行JavaScript失败')
  } finally {
    loading.js = false
  }
}

const takeScreenshot = async () => {
  loading.screenshot = true
  try {
    const response = await webApi.takeScreenshot()

    if (response.success) {
      screenshot.value = {
        url: response.url,
        timestamp: response.timestamp || Date.now()
      }
      ElMessage.success('截图成功')
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('截图失败')
  } finally {
    loading.screenshot = false
  }
}

const takeScreenshotWithDom = async () => {
  loading.domScreenshot = true
  try {
    const response = await webApi.takeScreenshotWithDom()

    if (response.base64_image) {
      screenshot.value = {
        url: `data:image/png;base64,${response.base64_image}`,
        timestamp: Date.now()
      }
      ElMessage.success('DOM截图成功')
    } else if (response.error) {
      ElMessage.error(response.error)
    } else {
      ElMessage.error('获取DOM截图失败')
    }
  } catch (error) {
    ElMessage.error('DOM截图失败')
  } finally {
    loading.domScreenshot = false
  }
}

const onDomElementClick = (element: any) => {
  ElMessage.info(`选中元素: ${element.seq_index} - ${element.payload?.text || '无文本'}`)
  // 这里可以添加更多的元素操作逻辑
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString()
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return `${minutes}分${remainingSeconds}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// 实时执行相关方法
const executeTask = async () => {
  loading.execution = true
  executionResult.value = null

  try {
    // 导入任务API
    const { taskApi } = await import('@/api/task')

    const response = await taskApi.executeDirectly({
      automation_type: 'web',
      input_type: taskForm.inputType,
      input_content: taskForm.inputContent,
      ai_model: taskForm.aiModel || undefined,
      temperature: taskForm.temperature,
      max_scroll_times: 3
    })

    if (response.success) {
      ElMessage.success('任务执行完成')
      executionResult.value = response

      // 清空表单
      taskForm.inputContent = ''
    } else {
      ElMessage.error(response.message || '任务执行失败')
      executionResult.value = response
    }
  } catch (error: any) {
    ElMessage.error('任务执行失败')
    executionResult.value = {
      success: false,
      message: '任务执行失败',
      error: error?.message || '未知错误'
    }
  } finally {
    loading.execution = false
    executionStatus.value.is_running = false
  }
}

const stopExecution = async () => {
  loading.stop = true

  try {
    const { taskApi } = await import('@/api/task')
    const response = await taskApi.stopExecution()

    if (response.success) {
      ElMessage.success('任务已停止')
      executionStatus.value.is_running = false
    } else {
      ElMessage.error(response.message || '停止任务失败')
    }
  } catch (error) {
    ElMessage.error('停止任务失败')
  } finally {
    loading.stop = false
  }
}

const checkExecutionStatus = async () => {
  try {
    const { taskApi } = await import('@/api/task')
    const response = await taskApi.getExecutionStatus()

    if (response.is_running) {
      executionStatus.value = {
        is_running: response.is_running,
        running_time: response.running_time || 0,
        completed_steps: response.completed_steps || 0
      }
    } else {
      executionStatus.value.is_running = false
    }
  } catch (error) {
    // 静默处理错误，避免频繁提示
    console.error('获取执行状态失败:', error)
  }
}

// 组件挂载
onMounted(() => {
  getBrowserStatus()

  // 定期检查执行状态
  const statusInterval = setInterval(() => {
    if (executionStatus.value.is_running) {
      checkExecutionStatus()
    }
  }, 2000)

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(statusInterval)
  })
})
</script>

<style lang="scss" scoped>
.web-page {
  padding: 24px;
  height: 100%;
  overflow: auto;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

// 固定卡片高度和内部滚动
.el-card {
  height: 500px;
  display: flex;
  flex-direction: column;

  :deep(.el-card__body) {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.browser-status {
  margin-top: 16px;
  
  .status-info {
    .status-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .label {
        font-weight: 500;
        margin-right: 8px;
        min-width: 80px;
      }
      
      .value {
        color: var(--el-text-color-regular);
        word-break: break-all;
      }
    }
  }
}

.config-section {
  .empty-tip {
    text-align: center;
    padding: 20px;
    color: var(--el-text-color-secondary);
  }
  
  .cookie-list, .header-list {
    margin: 16px 0;
    
    .cookie-item, .header-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }
  }
  
  .config-actions {
    margin-top: 16px;
    text-align: right;
  }
}

.js-result {
  margin-top: 16px;
  
  .result-header {
    margin-bottom: 12px;
  }
  
  .result-content {
    .result-text {
      background: var(--el-bg-color-page);
      padding: 12px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      line-height: 1.4;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}

.screenshot-container {
  text-align: center;
  
  .screenshot-image {
    max-width: 100%;
    max-height: 400px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .screenshot-info {
    margin-top: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.execution-status {
  margin-top: 16px;

  .status-info {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 12px;

    .status-details {
      flex: 1;

      p {
        margin: 4px 0;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.execution-result {
  margin-top: 16px;

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .execution-time {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }

  .result-message {
    padding: 12px;
    background: var(--el-bg-color-page);
    border-radius: 4px;
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.5;
  }

  .error-details {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: var(--el-color-danger);
    }

    pre {
      background: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
      padding: 12px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      line-height: 1.4;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}

// 窗口大小设置样式
.viewport-settings {
  width: 100%;
}

.viewport-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #666;
}

.viewport-presets {
  margin-top: 8px;
  margin-bottom: 8px;
}

.viewport-hint {
  margin-top: 8px;
}

// 调试操作模块样式
.debug-operations-card {
  position: relative;
}

.debug-operations-card .warning-tip {
  position: absolute;
  top: 60px; /* 为header留出空间 */
  left: 16px;
  right: 16px;
  bottom: 16px;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .cookie-item, .header-item {
    flex-direction: column;
    align-items: stretch !important;

    .el-input {
      width: 100% !important;
    }
  }
}
</style>
