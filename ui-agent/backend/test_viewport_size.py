#!/usr/bin/env python3
"""
测试窗口大小设置功能的脚本
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_viewport_size_setting():
    """测试窗口大小设置功能"""
    print("=== 测试窗口大小设置功能 ===")
    
    # 测试用例
    test_cases = [
        {
            "name": "默认桌面模式",
            "payload": {
                "url": "https://www.baidu.com",
                "is_h5_mode": False
            },
            "expected_size": {"width": 1920, "height": 1080}
        },
        {
            "name": "默认移动模式",
            "payload": {
                "url": "https://www.baidu.com",
                "is_h5_mode": True
            },
            "expected_size": {"width": 375, "height": 667}
        },
        {
            "name": "自定义窗口大小 - 1366x768",
            "payload": {
                "url": "https://www.baidu.com",
                "is_h5_mode": False,
                "viewport_width": 1366,
                "viewport_height": 768
            },
            "expected_size": {"width": 1366, "height": 768}
        },
        {
            "name": "自定义窗口大小 - iPhone XR",
            "payload": {
                "url": "https://www.baidu.com",
                "is_h5_mode": True,
                "viewport_width": 414,
                "viewport_height": 896
            },
            "expected_size": {"width": 414, "height": 896}
        },
        {
            "name": "自定义窗口大小覆盖H5模式",
            "payload": {
                "url": "https://www.baidu.com",
                "is_h5_mode": True,  # H5模式
                "viewport_width": 1920,  # 但指定桌面大小
                "viewport_height": 1080
            },
            "expected_size": {"width": 1920, "height": 1080}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        
        try:
            # 先重置浏览器
            reset_response = requests.post(f"{BASE_URL}/api/web/reset")
            print(f"重置浏览器: {reset_response.json()}")
            
            # 导航到页面
            response = requests.post(
                f"{BASE_URL}/api/web/navigate",
                json=test_case["payload"],
                headers={"Content-Type": "application/json"}
            )
            
            result = response.json()
            print(f"导航结果: {result}")
            
            if result.get("success"):
                # 获取浏览器状态检查视口大小
                status_response = requests.get(f"{BASE_URL}/api/web/status")
                status = status_response.json()
                
                if status.get("success") and status.get("viewport"):
                    actual_size = status["viewport"]
                    expected_size = test_case["expected_size"]
                    
                    print(f"期望大小: {expected_size}")
                    print(f"实际大小: {actual_size}")
                    
                    if (actual_size["width"] == expected_size["width"] and 
                        actual_size["height"] == expected_size["height"]):
                        print("✓ 窗口大小设置正确")
                    else:
                        print("✗ 窗口大小设置错误")
                else:
                    print("✗ 无法获取浏览器状态")
            else:
                print(f"✗ 导航失败: {result.get('message')}")
                
        except Exception as e:
            print(f"✗ 测试异常: {e}")

def test_invalid_viewport_sizes():
    """测试无效的窗口大小"""
    print("\n=== 测试无效窗口大小 ===")
    
    invalid_cases = [
        {
            "name": "只设置宽度",
            "payload": {
                "url": "https://www.baidu.com",
                "viewport_width": 1366
                # 缺少 viewport_height
            }
        },
        {
            "name": "只设置高度",
            "payload": {
                "url": "https://www.baidu.com",
                "viewport_height": 768
                # 缺少 viewport_width
            }
        },
        {
            "name": "零值大小",
            "payload": {
                "url": "https://www.baidu.com",
                "viewport_width": 0,
                "viewport_height": 0
            }
        },
        {
            "name": "负值大小",
            "payload": {
                "url": "https://www.baidu.com",
                "viewport_width": -100,
                "viewport_height": -100
            }
        }
    ]
    
    for i, test_case in enumerate(invalid_cases, 1):
        print(f"\n--- 无效测试 {i}: {test_case['name']} ---")
        
        try:
            # 先重置浏览器
            reset_response = requests.post(f"{BASE_URL}/api/web/reset")
            
            # 导航到页面
            response = requests.post(
                f"{BASE_URL}/api/web/navigate",
                json=test_case["payload"],
                headers={"Content-Type": "application/json"}
            )
            
            result = response.json()
            print(f"导航结果: {result}")
            
            if result.get("success"):
                # 获取浏览器状态检查是否使用了默认大小
                status_response = requests.get(f"{BASE_URL}/api/web/status")
                status = status_response.json()
                
                if status.get("success") and status.get("viewport"):
                    actual_size = status["viewport"]
                    print(f"实际大小: {actual_size}")
                    
                    # 应该使用默认大小（桌面模式 1920x1080）
                    if (actual_size["width"] == 1920 and actual_size["height"] == 1080):
                        print("✓ 正确使用默认大小")
                    else:
                        print("? 使用了其他大小（可能是有效的处理方式）")
                else:
                    print("✗ 无法获取浏览器状态")
            else:
                print(f"导航失败: {result.get('message')}")
                
        except Exception as e:
            print(f"✗ 测试异常: {e}")

def test_viewport_presets():
    """测试预设窗口大小"""
    print("\n=== 测试预设窗口大小 ===")
    
    presets = [
        {"name": "1920×1080 (Full HD)", "width": 1920, "height": 1080},
        {"name": "1366×768 (常见笔记本)", "width": 1366, "height": 768},
        {"name": "375×667 (iPhone SE)", "width": 375, "height": 667},
        {"name": "414×896 (iPhone XR)", "width": 414, "height": 896}
    ]
    
    for preset in presets:
        print(f"\n--- 测试预设: {preset['name']} ---")
        
        try:
            # 先重置浏览器
            reset_response = requests.post(f"{BASE_URL}/api/web/reset")
            
            # 使用预设大小导航
            payload = {
                "url": "https://www.baidu.com",
                "viewport_width": preset["width"],
                "viewport_height": preset["height"]
            }
            
            response = requests.post(
                f"{BASE_URL}/api/web/navigate",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            result = response.json()
            
            if result.get("success"):
                # 验证窗口大小
                status_response = requests.get(f"{BASE_URL}/api/web/status")
                status = status_response.json()
                
                if status.get("success") and status.get("viewport"):
                    actual_size = status["viewport"]
                    
                    if (actual_size["width"] == preset["width"] and 
                        actual_size["height"] == preset["height"]):
                        print(f"✓ {preset['name']} 设置成功")
                    else:
                        print(f"✗ {preset['name']} 设置失败")
                        print(f"  期望: {preset['width']}x{preset['height']}")
                        print(f"  实际: {actual_size['width']}x{actual_size['height']}")
                else:
                    print(f"✗ 无法获取 {preset['name']} 的状态")
            else:
                print(f"✗ {preset['name']} 导航失败: {result.get('message')}")
                
        except Exception as e:
            print(f"✗ {preset['name']} 测试异常: {e}")

if __name__ == "__main__":
    print("开始测试窗口大小设置功能...")
    
    # 测试基本功能
    test_viewport_size_setting()
    
    # 测试无效输入
    test_invalid_viewport_sizes()
    
    # 测试预设大小
    test_viewport_presets()
    
    print("\n🎉 窗口大小设置功能测试完成！")
