"""
Android端服务
"""

import os
import subprocess
import asyncio
import requests
import time
import base64
import uuid
import threading
import random
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import xml.etree.ElementTree as ET

from app.models.device import AndroidDevice, AppPackage, ApkInstallTask
from app.core.config import settings

class AndroidService:
    """Android端服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.current_device = None
        self.adb_path = self._find_adb_path()

    def _find_adb_path(self) -> str:
        """查找ADB工具路径"""
        # 常见的ADB路径
        possible_paths = [
            "adb",  # 系统PATH中的adb
            "/Applications/AirtestIDE.app/Contents/MacOS/airtest/core/android/static/adb/mac/adb",
            "/Applications/QtScrcpy.app/Contents/MacOS/adb",
            "/usr/local/bin/adb",
            "/opt/homebrew/bin/adb",
            "~/Library/Android/sdk/platform-tools/adb",
            "~/Android/Sdk/platform-tools/adb"
        ]

        for path in possible_paths:
            try:
                # 展开用户目录
                expanded_path = os.path.expanduser(path)
                result = subprocess.run(
                    [expanded_path, "version"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    print(f"找到ADB工具: {expanded_path}")
                    return expanded_path
            except (FileNotFoundError, subprocess.TimeoutExpired):
                continue

        print("警告: 未找到ADB工具，请确保已安装Android SDK")
        return "adb"  # 回退到默认值
        
    async def get_devices(self) -> List[Dict[str, Any]]:
        """获取Android设备列表"""
        try:
            # 首先检查adb是否可用
            adb_check = subprocess.run(
                [self.adb_path, "version"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if adb_check.returncode != 0:
                print(f"ADB工具不可用: {self.adb_path}")
                return []

            # 执行adb devices命令
            result = subprocess.run(
                [self.adb_path, "devices"],
                capture_output=True,
                text=True,
                timeout=10
            )

            print(f"ADB devices 输出: {result.stdout}")
            print(f"ADB devices 错误: {result.stderr}")

            devices = []
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行标题
                print(f"解析的设备行: {lines}")

                for line in lines:
                    if line.strip() and '\t' in line:
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            device_id = parts[0].strip()
                            status = parts[1].strip()
                            print(f"发现设备: {device_id}, 状态: {status}")

                            if status == 'device':
                                device_info = await self._get_device_info(device_id)
                                devices.append(device_info)
                            elif status in ['unauthorized', 'offline']:
                                # 也添加未授权或离线的设备，但标记为未连接
                                device_info = {
                                    "device_id": device_id,
                                    "device_name": f"Device {device_id} ({status})",
                                    "is_connected": False,
                                    "is_active": False,
                                    "status": status
                                }
                                devices.append(device_info)
            else:
                print(f"ADB命令执行失败，返回码: {result.returncode}")
                print(f"错误信息: {result.stderr}")

            print(f"最终设备列表: {devices}")

            # 更新数据库中的设备信息
            await self._update_devices_in_db(devices)

            return devices

        except FileNotFoundError:
            print("ADB命令未找到，请确保已安装Android SDK并配置环境变量")
            return []
        except subprocess.TimeoutExpired:
            print("ADB命令执行超时")
            return []
        except Exception as e:
            print(f"获取设备列表失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def _get_device_info(self, device_id: str) -> Dict[str, Any]:
        """获取设备详细信息"""
        try:
            # 获取设备属性
            props = {}
            prop_commands = {
                'brand': 'ro.product.brand',
                'model': 'ro.product.model',
                'android_version': 'ro.build.version.release'
            }
            
            for key, prop in prop_commands.items():
                result = subprocess.run(
                    [self.adb_path, "-s", device_id, "shell", "getprop", prop],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    props[key] = result.stdout.strip()

            # 获取屏幕分辨率
            result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "wm", "size"],
                capture_output=True,
                text=True,
                timeout=5
            )
            screen_resolution = "unknown"
            if result.returncode == 0:
                output = result.stdout.strip()
                if "Physical size:" in output:
                    screen_resolution = output.split("Physical size: ")[1]
            
            return {
                "device_id": device_id,
                "device_name": f"{props.get('brand', 'Unknown')} {props.get('model', 'Device')}",
                "brand": props.get('brand'),
                "model": props.get('model'),
                "android_version": props.get('android_version'),
                "screen_resolution": screen_resolution,
                "is_connected": True,
                "is_active": False
            }
            
        except Exception as e:
            print(f"获取设备信息失败: {e}")
            return {
                "device_id": device_id,
                "device_name": f"Device {device_id}",
                "is_connected": True,
                "is_active": False
            }
    
    async def _update_devices_in_db(self, devices: List[Dict[str, Any]]):
        """更新数据库中的设备信息"""
        try:
            # 获取当前数据库中的设备
            db_devices = self.db.query(AndroidDevice).all()
            db_device_ids = {device.device_id for device in db_devices}
            
            # 更新或创建设备记录
            for device_info in devices:
                device_id = device_info["device_id"]
                
                if device_id in db_device_ids:
                    # 更新现有设备
                    device = self.db.query(AndroidDevice).filter(
                        AndroidDevice.device_id == device_id
                    ).first()
                    if device:
                        device.device_name = device_info.get("device_name")
                        device.brand = device_info.get("brand")
                        device.model = device_info.get("model")
                        device.android_version = device_info.get("android_version")
                        device.screen_resolution = device_info.get("screen_resolution")
                        device.is_connected = True
                        # 保持原有的is_active状态，并更新到device_info中
                        device_info["is_active"] = device.is_active
                else:
                    # 创建新设备
                    device = AndroidDevice(
                        device_id=device_id,
                        device_name=device_info.get("device_name"),
                        brand=device_info.get("brand"),
                        model=device_info.get("model"),
                        android_version=device_info.get("android_version"),
                        screen_resolution=device_info.get("screen_resolution"),
                        is_connected=True,
                        is_active=False
                    )
                    self.db.add(device)
                    device_info["is_active"] = False
            
            # 标记未连接的设备
            current_device_ids = {device["device_id"] for device in devices}
            for device in db_devices:
                if device.device_id not in current_device_ids:
                    device.is_connected = False
            
            self.db.commit()
            
        except Exception as e:
            print(f"更新设备数据库失败: {e}")
            self.db.rollback()
    
    async def select_device(self, device_id: str) -> Dict[str, Any]:
        """选择当前活动设备"""
        try:
            # 取消所有设备的活动状态
            self.db.query(AndroidDevice).update({"is_active": False})

            # 设置指定设备为活动状态
            device = self.db.query(AndroidDevice).filter(
                AndroidDevice.device_id == device_id
            ).first()

            if not device:
                self.db.rollback()
                return {"success": False, "message": "设备不存在"}

            if not device.is_connected:
                self.db.rollback()
                return {"success": False, "message": "设备未连接"}

            device.is_active = True
            self.db.commit()

            # 刷新设备对象以确保状态同步
            self.db.refresh(device)
            self.current_device = device

            return {
                "success": True,
                "message": f"已选择设备: {device.device_name}",
                "device": {
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "brand": device.brand,
                    "model": device.model,
                    "is_active": device.is_active
                }
            }

        except Exception as e:
            self.db.rollback()
            return {"success": False, "message": f"选择设备失败: {str(e)}"}
    
    async def refresh_devices(self) -> List[Dict[str, Any]]:
        """刷新设备列表"""
        return await self.get_devices()
    
    async def install_apk(self, download_url: str, device_id: Optional[str] = None) -> Dict[str, Any]:
        """创建APK安装任务（异步）"""
        try:
            # 如果没有指定设备，使用当前活动设备
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 生成文件名
            filename = download_url.split('/')[-1]
            if not filename.endswith('.apk'):
                filename = f"{int(time.time())}.apk"

            # 创建安装任务记录
            install_task = ApkInstallTask(
                task_id=task_id,
                download_url=download_url,
                device_id=device_id,
                filename=filename,
                status="pending"
            )
            self.db.add(install_task)
            self.db.commit()

            # 启动后台线程执行安装任务
            thread = threading.Thread(target=self._install_apk_sync, args=(task_id,))
            thread.daemon = True
            thread.start()

            return {
                "success": True,
                "message": "APK安装任务已创建",
                "task_id": task_id
            }

        except Exception as e:
            return {"success": False, "message": f"创建安装任务失败: {str(e)}"}

    def _install_apk_sync(self, task_id: str):
        """同步执行APK下载和安装（在线程池中运行）"""
        # 创建新的数据库会话
        from app.core.database import SessionLocal
        db = SessionLocal()

        try:
            # 获取任务信息
            task = db.query(ApkInstallTask).filter(ApkInstallTask.task_id == task_id).first()
            if not task:
                return

            print(f"开始处理APK安装任务: {task_id}")

            # 更新状态为下载中
            task.status = "downloading"
            task.progress = 0
            db.commit()
            print(f"任务状态更新为: downloading")

            # 创建下载目录
            download_dir = settings.OUTPUT_DIR / "downloads"
            download_dir.mkdir(exist_ok=True)
            file_path = download_dir / task.filename

            print(f"开始下载APK: {task.download_url}")

            # 下载文件
            response = requests.get(task.download_url, stream=True, timeout=30)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            task.total_size = total_size
            db.commit()

            print(f"文件总大小: {total_size} bytes")

            downloaded_size = 0
            start_time = time.time()
            last_update_time = start_time

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        # 每2秒更新一次进度
                        current_time = time.time()
                        if current_time - last_update_time >= 2:
                            if total_size > 0:
                                progress = int((downloaded_size / total_size) * 80)  # 下载占80%
                                task.progress = progress
                                task.downloaded_size = downloaded_size

                                # 计算下载速度
                                elapsed_time = current_time - start_time
                                if elapsed_time > 0:
                                    speed = downloaded_size / elapsed_time / 1024 / 1024  # MB/s
                                    task.download_speed = f"{speed:.2f} MB/s"

                                db.commit()
                                print(f"下载进度: {progress}%, 速度: {task.download_speed}")
                                last_update_time = current_time

            print(f"下载完成: {file_path}")

            # 更新状态为安装中
            task.status = "installing"
            task.progress = 85
            task.file_path = str(file_path)
            db.commit()

            print(f"开始安装APK到设备: {task.device_id}")

            # 安装APK
            result = subprocess.run(
                ["/Applications/AirtestIDE.app/Contents/MacOS/airtest/core/android/static/adb/mac/adb",
                 "-s", task.device_id, "install", str(file_path)],
                capture_output=True,
                text=True,
                timeout=120
            )

            if result.returncode == 0:
                # 安装成功
                task.status = "completed"
                task.progress = 100
                task.completed_at = datetime.now()

                print(f"APK安装成功: {task.filename}")

                # 记录到应用包表
                app_package = AppPackage(
                    download_url=task.download_url,
                    file_path=str(file_path),
                    file_size=file_path.stat().st_size,
                    is_downloaded=True,
                    is_installed=True,
                    device_id=task.device_id
                )
                db.add(app_package)

            else:
                # 安装失败
                task.status = "failed"
                task.error_message = f"安装失败: {result.stderr}"
                print(f"APK安装失败: {result.stderr}")

            db.commit()

        except Exception as e:
            print(f"APK安装任务异常: {e}")
            # 更新任务状态为失败
            try:
                task = db.query(ApkInstallTask).filter(ApkInstallTask.task_id == task_id).first()
                if task:
                    task.status = "failed"
                    task.error_message = str(e)
                    db.commit()
            except:
                pass

        finally:
            db.close()

    async def get_poco_dom_tree(self, device_id: Optional[str] = None) -> Dict[str, Any]:
        """通过poco获取Android设备的DOM树"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 使用uiautomator获取DOM树的简化版本
            ui_dump_script = f'''
import subprocess
import xml.etree.ElementTree as ET
import json

def parse_ui_xml_to_poco_format(xml_content):
    """将uiautomator的XML转换为poco格式的DOM树"""
    try:
        root = ET.fromstring(xml_content)

        def xml_to_poco_node(element, seq_index_counter):
            # 获取属性
            bounds = element.get('bounds', '')
            text = element.get('text', '')
            content_desc = element.get('content-desc', '')
            class_name = element.get('class', '')
            clickable = element.get('clickable', 'false') == 'true'
            enabled = element.get('enabled', 'true') == 'true'

            # 解析bounds坐标 [x1,y1][x2,y2]
            pos = [0.5, 0.5]  # 默认中心位置
            size = [0.1, 0.1]  # 默认大小

            if bounds and '[' in bounds:
                try:
                    # 解析 [x1,y1][x2,y2] 格式
                    coords = bounds.replace('[', '').replace(']', ',').split(',')
                    if len(coords) >= 4:
                        x1, y1, x2, y2 = map(int, coords[:4])
                        # 假设屏幕分辨率为1080x2340 (从之前的设备信息)
                        screen_width, screen_height = 1080, 2340

                        # 计算相对位置和大小
                        center_x = (x1 + x2) / 2 / screen_width
                        center_y = (y1 + y2) / 2 / screen_height
                        width = (x2 - x1) / screen_width
                        height = (y2 - y1) / screen_height

                        pos = [center_x, center_y]
                        size = [width, height]
                except:
                    pass

            # 构建poco格式的节点
            node = {{
                "name": class_name,
                "payload": {{
                    "name": class_name,
                    "type": class_name,
                    "text": text or content_desc,
                    "pos": pos,
                    "size": size,
                    "clickable": clickable,
                    "enabled": enabled,
                    "bounds": bounds
                }},
                "children": []
            }}

            # 添加seq_index
            node["seq_index"] = seq_index_counter[0]
            seq_index_counter[0] += 1

            # 递归处理子元素
            for child_element in element:
                child_node = xml_to_poco_node(child_element, seq_index_counter)
                node["children"].append(child_node)

            return node

        seq_counter = [0]
        return xml_to_poco_node(root, seq_counter)

    except Exception as e:
        return {{"error": str(e)}}

try:
    # 获取UI层次结构
    result = subprocess.run([
        "adb", "-s", "{device_id}", "shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"
    ], capture_output=True, text=True, timeout=10)

    if result.returncode != 0:
        print(f"ERROR: UI dump failed: {{result.stderr}}")
        exit(1)

    # 拉取XML文件
    result = subprocess.run([
        "adb", "-s", "{device_id}", "shell", "cat", "/sdcard/ui_dump.xml"
    ], capture_output=True, text=True, timeout=10)

    if result.returncode != 0:
        print(f"ERROR: Failed to read XML: {{result.stderr}}")
        exit(1)

    xml_content = result.stdout
    dom_tree = parse_ui_xml_to_poco_format(xml_content)

    print("DOM_TREE_START")
    print(json.dumps(dom_tree, ensure_ascii=False))
    print("DOM_TREE_END")

except Exception as e:
    print(f"ERROR: {{str(e)}}")
'''

            # 执行UI dump脚本
            result = subprocess.run(
                ["python3", "-c", ui_dump_script],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                # 解析输出，提取DOM树JSON
                output = result.stdout
                start_marker = "DOM_TREE_START"
                end_marker = "DOM_TREE_END"

                start_idx = output.find(start_marker)
                end_idx = output.find(end_marker)

                if start_idx != -1 and end_idx != -1:
                    json_str = output[start_idx + len(start_marker):end_idx].strip()
                    import json
                    dom_tree = json.loads(json_str)

                    return {
                        "success": True,
                        "dom_tree": dom_tree,
                        "message": "DOM树获取成功"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"解析DOM树失败: {output}"
                    }
            else:
                return {
                    "success": False,
                    "message": f"获取DOM树失败: {result.stderr}"
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"获取poco DOM树失败: {str(e)}"
            }

    def _draw_poco_bounding_boxes(self, screenshot_path: str, dom_tree: Dict[str, Any]):
        """在Android截图上绘制poco DOM树的边界框"""
        try:
            # 打开截图
            img = Image.open(screenshot_path)
            draw = ImageDraw.Draw(img, 'RGBA')

            # 获取屏幕分辨率
            screen_width, screen_height = img.size

            # 尝试加载字体
            try:
                font = ImageFont.truetype("Arial", 14)
            except:
                font = ImageFont.load_default()

            # 重置随机种子
            random.seed(42)

            # 递归绘制边界框
            self._draw_poco_node_bounding_box(draw, dom_tree, screen_width, screen_height, font)

            return img

        except Exception as e:
            print(f"绘制poco边界框失败: {e}")
            return Image.open(screenshot_path)

    def _draw_poco_node_bounding_box(self, draw, node, screen_width, screen_height, font):
        """递归地为poco DOM树的每个节点绘制边界框"""
        # 检查节点是否有位置和大小信息
        if "payload" in node and "pos" in node["payload"] and "size" in node["payload"]:
            # 获取节点的位置和大小
            pos = node["payload"]["pos"]
            size = node["payload"]["size"]

            # 计算边界框坐标 (poco使用相对坐标 0-1)
            x1 = int((pos[0] - size[0]/2) * screen_width)
            y1 = int((pos[1] - size[1]/2) * screen_height)
            x2 = int((pos[0] + size[0]/2) * screen_width)
            y2 = int((pos[1] + size[1]/2) * screen_height)

            # 确保坐标在屏幕范围内
            x1 = max(0, min(x1, screen_width))
            y1 = max(0, min(y1, screen_height))
            x2 = max(0, min(x2, screen_width))
            y2 = max(0, min(y2, screen_height))

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")

            # 构建标签文本
            label_text = f"{seq_index}"

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框 - 使用随机颜色，增加线条宽度以提高可见性
            if x2 > x1 and y2 > y1:  # 确保有有效的矩形
                draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)

                # 绘制标签背景
                text_bbox = draw.textbbox((0, 0), label_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

                # 标签位置
                label_x = x1
                label_y = max(0, y1 - text_height - 2)

                # 绘制标签背景
                draw.rectangle(
                    [label_x, label_y, label_x + text_width + 4, label_y + text_height + 2],
                    fill=border_color
                )

                # 绘制标签文本
                draw.text((label_x + 2, label_y + 1), label_text, fill="white", font=font)

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_poco_node_bounding_box(draw, child, screen_width, screen_height, font)

    def _generate_random_color(self, seed_value):
        """根据种子值生成随机颜色"""
        # 使用种子值生成一致的随机颜色
        random.seed(hash(str(seed_value)) % 1000)
        r = random.randint(50, 255)
        g = random.randint(50, 255)
        b = random.randint(50, 255)
        return (r, g, b, 200)  # 带透明度的颜色

    async def take_screenshot_with_poco_dom(self, device_id: Optional[str] = None) -> Dict[str, Any]:
        """获取设备截图并绘制poco DOM树边界框"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 获取poco DOM树
            dom_result = await self.get_poco_dom_tree(device_id)
            if not dom_result["success"]:
                return dom_result

            # 获取截图
            screenshot_result = await self.take_screenshot(device_id)
            if not screenshot_result["success"]:
                return screenshot_result

            # 在截图上绘制poco DOM树边界框
            annotated_image = self._draw_poco_bounding_boxes(
                screenshot_result["file_path"],
                dom_result["dom_tree"]
            )

            # 保存标注后的图像
            timestamp = int(time.time())
            filename = f"android_poco_annotated_{timestamp}.png"
            annotated_file_path = settings.SCREENSHOTS_DIR / filename
            annotated_image.save(annotated_file_path)

            # 编码为base64
            base64_image = self._encode_image_to_base64(str(annotated_file_path))

            return {
                "success": True,
                "base64_image": base64_image,
                "dom_tree": dom_result["dom_tree"],
                "screenshot_path": str(annotated_file_path),
                "url": f"/output/screenshots/{filename}",
                "message": "截图和DOM树获取成功"
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"获取poco截图失败: {str(e)}"
            }

    async def get_apk_install_status(self, task_id: str) -> Dict[str, Any]:
        """获取APK安装任务状态"""
        try:
            task = self.db.query(ApkInstallTask).filter(ApkInstallTask.task_id == task_id).first()
            if not task:
                return {"success": False, "message": "任务不存在"}

            return {
                "success": True,
                "task_id": task.task_id,
                "status": task.status,
                "progress": task.progress,
                "filename": task.filename,
                "total_size": task.total_size,
                "downloaded_size": task.downloaded_size,
                "download_speed": task.download_speed,
                "error_message": task.error_message,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None
            }

        except Exception as e:
            return {"success": False, "message": f"获取任务状态失败: {str(e)}"}

    async def get_apk_install_tasks(self, device_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取APK安装任务列表"""
        try:
            query = self.db.query(ApkInstallTask)
            if device_id:
                query = query.filter(ApkInstallTask.device_id == device_id)

            tasks = query.order_by(ApkInstallTask.created_at.desc()).limit(20).all()

            result = []
            for task in tasks:
                result.append({
                    "task_id": task.task_id,
                    "status": task.status,
                    "progress": task.progress,
                    "filename": task.filename,
                    "download_url": task.download_url,
                    "device_id": task.device_id,
                    "total_size": task.total_size,
                    "downloaded_size": task.downloaded_size,
                    "download_speed": task.download_speed,
                    "error_message": task.error_message,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None
                })

            return result

        except Exception as e:
            return []
    
    async def execute_adb_command(self, command: str, device_id: Optional[str] = None) -> Dict[str, Any]:
        """执行ADB命令"""
        try:
            # 如果没有指定设备，使用当前活动设备
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id
            
            # 构建完整的ADB命令
            full_command = [self.adb_path, "-s", device_id] + command.split()
            
            # 执行命令
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            return {
                "success": result.returncode == 0,
                "command": " ".join(full_command),
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
        except Exception as e:
            return {"success": False, "message": f"执行ADB命令失败: {str(e)}"}
    
    async def take_screenshot(self, device_id: str) -> Dict[str, Any]:
        """获取设备截图"""
        try:
            # 生成截图文件名
            timestamp = int(asyncio.get_event_loop().time())
            filename = f"android_screenshot_{timestamp}.png"
            file_path = settings.SCREENSHOTS_DIR / filename
            
            # 执行截图命令
            result = subprocess.run(
                [self.adb_path, "-s", device_id, "exec-out", "screencap", "-p"],
                capture_output=True,
                timeout=10
            )
            
            if result.returncode == 0:
                with open(file_path, 'wb') as f:
                    f.write(result.stdout)
                
                return {
                    "success": True,
                    "file_path": str(file_path),
                    "url": f"/output/screenshots/{filename}"
                }
            else:
                return {"success": False, "message": "截图失败"}
                
        except Exception as e:
            return {"success": False, "message": f"获取截图失败: {str(e)}"}
    
    async def get_device_status(self, device_id: str) -> Dict[str, Any]:
        """获取设备状态"""
        try:
            device = self.db.query(AndroidDevice).filter(
                AndroidDevice.device_id == device_id
            ).first()
            
            if not device:
                return {"success": False, "message": "设备不存在"}
            
            return {
                "success": True,
                "device": {
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "is_connected": device.is_connected,
                    "is_active": device.is_active,
                    "brand": device.brand,
                    "model": device.model,
                    "android_version": device.android_version,
                    "screen_resolution": device.screen_resolution
                }
            }
            
        except Exception as e:
            return {"success": False, "message": f"获取设备状态失败: {str(e)}"}

    # Android端操作方法
    async def get_dom_tree_and_page_screenshot(self, device_id: Optional[str] = None) -> Dict[str, Any]:
        """获取Android设备的DOM树和截图"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"error": "没有活动设备"}
                device_id = active_device.device_id

            # 获取UI层次结构
            ui_dump_result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if ui_dump_result.returncode != 0:
                return {"error": f"获取UI层次结构失败: {ui_dump_result.stderr}"}

            # 拉取UI层次结构文件
            pull_result = subprocess.run(
                [self.adb_path, "-s", device_id, "pull", "/sdcard/ui_dump.xml", "/tmp/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if pull_result.returncode != 0:
                return {"error": f"拉取UI文件失败: {pull_result.stderr}"}

            # 解析XML文件
            dom_tree = self._parse_android_ui_xml("/tmp/ui_dump.xml")

            # 获取屏幕截图
            screenshot_result = await self.take_screenshot(device_id)
            if not screenshot_result["success"]:
                return {"error": f"获取截图失败: {screenshot_result['message']}"}

            # 在截图上绘制边界框
            annotated_image = self._draw_android_bounding_boxes(
                screenshot_result["file_path"],
                dom_tree
            )

            # 保存标注后的图像
            timestamp = int(time.time())
            filename = f"android_annotated_{timestamp}.png"
            annotated_file_path = settings.SCREENSHOTS_DIR / filename
            annotated_image.save(annotated_file_path)

            # 编码为base64
            base64_image = self._encode_image_to_base64(str(annotated_file_path))

            # 获取屏幕信息
            screen_info = self._get_screen_info(device_id)

            return {
                "base64_image": base64_image,
                "viewport": screen_info,
                "is_scrollable": self._check_if_scrollable(dom_tree),
                "dom_tree": dom_tree,
                "screenshot_path": str(annotated_file_path)
            }

        except Exception as e:
            return {"error": f"获取Android DOM树和截图失败: {str(e)}"}

    def _parse_android_ui_xml(self, xml_file_path: str) -> Dict[str, Any]:
        """解析Android UI XML文件"""
        try:
            tree = ET.parse(xml_file_path)
            root = tree.getroot()

            # 递归解析节点
            dom_tree = self._parse_android_node(root, 0)

            # 添加全局顺序索引
            self._add_sequential_index(dom_tree)

            return dom_tree

        except Exception as e:
            print(f"解析Android UI XML失败: {e}")
            return {}

    def _parse_android_node(self, node, index: int) -> Dict[str, Any]:
        """递归解析Android UI节点"""
        result = {
            "index": index,
            "tag": node.tag,
            "type": "element",
            "payload": {}
        }

        # 解析属性
        for attr_name, attr_value in node.attrib.items():
            if attr_name == "bounds":
                # 解析bounds属性，格式如 "[0,0][1080,1920]"
                bounds = self._parse_bounds(attr_value)
                if bounds:
                    result["payload"]["rect"] = bounds
            elif attr_name in ["text", "content-desc", "resource-id", "class", "package"]:
                if attr_value:
                    result["payload"][attr_name] = attr_value
            elif attr_name in ["clickable", "scrollable", "focusable", "enabled", "selected", "checked"]:
                result["payload"][attr_name] = attr_value.lower() == "true"

        # 处理子节点
        if len(node) > 0:
            children = []
            for i, child in enumerate(node):
                child_result = self._parse_android_node(child, i)
                if child_result:
                    children.append(child_result)
            if children:
                result["children"] = children

        return result

    def _parse_bounds(self, bounds_str: str) -> Optional[Dict[str, int]]:
        """解析bounds字符串，如 '[0,0][1080,1920]'"""
        try:
            import re
            pattern = r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]'
            match = re.match(pattern, bounds_str)
            if match:
                x1, y1, x2, y2 = map(int, match.groups())
                return {
                    "x": x1,
                    "y": y1,
                    "width": x2 - x1,
                    "height": y2 - y1
                }
        except Exception as e:
            print(f"解析bounds失败: {e}")
        return None

    def _add_sequential_index(self, node: Dict[str, Any], index_map: Optional[Dict[str, int]] = None):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}

        node["seq_index"] = index_map["current"]
        index_map["current"] += 1

        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)

    def _draw_android_bounding_boxes(self, screenshot_path: str, dom_tree: Dict[str, Any]):
        """在Android截图上绘制边界框"""
        try:
            # 打开截图
            img = Image.open(screenshot_path)
            draw = ImageDraw.Draw(img, 'RGBA')

            # 尝试加载字体
            try:
                font = ImageFont.truetype("Arial", 14)
            except:
                font = ImageFont.load_default()

            # 重置随机种子
            import random
            random.seed(42)

            # 递归绘制边界框
            self._draw_android_node_bounding_box(draw, dom_tree, img.size, font)

            return img

        except Exception as e:
            print(f"绘制Android边界框失败: {e}")
            return Image.open(screenshot_path)

    def _draw_android_node_bounding_box(self, draw, node, image_size, font):
        """递归地为Android节点绘制边界框"""
        if "payload" in node and "rect" in node["payload"]:
            rect = node["payload"]["rect"]

            x1 = rect["x"]
            y1 = rect["y"]
            x2 = x1 + rect["width"]
            y2 = y1 + rect["height"]

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框
            draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)

            # 构建标签文本
            label_text = str(seq_index)

            # 添加元素信息到标签
            if "text" in node["payload"] and node["payload"]["text"]:
                text = node["payload"]["text"][:10]  # 限制长度
                label_text += f":{text}"
            elif "content-desc" in node["payload"] and node["payload"]["content-desc"]:
                desc = node["payload"]["content-desc"][:10]
                label_text += f":{desc}"

            # 绘制标签
            if label_text:
                try:
                    # 计算文本大小
                    try:
                        bbox = draw.textbbox((0, 0), label_text, font=font)
                        text_width = bbox[2] - bbox[0] + 6
                        text_height = bbox[3] - bbox[1] + 4
                    except:
                        text_width = len(label_text) * 8 + 6
                        text_height = 16

                    # 确保标签不会超出屏幕边界
                    label_x = x1
                    if label_x + text_width > image_size[0]:
                        label_x = image_size[0] - text_width
                    if label_x < 0:
                        label_x = 0

                    label_y = y1 - text_height
                    if label_y < 0:
                        label_y = y2
                        if label_y + text_height > image_size[1]:
                            label_y = image_size[1] - text_height

                    # 绘制标签背景
                    bg_color = border_color + (200,)
                    draw.rectangle(
                        [label_x, label_y, label_x + text_width, label_y + text_height],
                        fill=bg_color
                    )

                    # 绘制文本
                    text_color = self._get_contrasting_text_color(border_color)
                    draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)

                except Exception as e:
                    print(f"绘制Android标签失败: {e}")

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_android_node_bounding_box(draw, child, image_size, font)

    def _generate_random_color(self, seq_index):
        """根据seq_index生成随机但一致的颜色"""
        import random
        import colorsys

        random.seed(hash(str(seq_index)) % 2147483647)

        hue = random.random()
        saturation = 0.7 + random.random() * 0.3
        lightness = 0.4 + random.random() * 0.3

        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        return tuple(int(c * 255) for c in rgb)

    def _get_contrasting_text_color(self, bg_color):
        """根据背景颜色获取对比度高的文本颜色"""
        r, g, b = bg_color[:3]
        brightness = (r * 299 + g * 587 + b * 114) / 1000
        return (255, 255, 255) if brightness < 128 else (0, 0, 0)

    def _get_screen_info(self, device_id: str) -> Dict[str, Any]:
        """获取屏幕信息"""
        try:
            # 获取屏幕分辨率
            result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "wm", "size"],
                capture_output=True,
                text=True,
                timeout=5
            )

            width, height = 1080, 1920  # 默认值
            if result.returncode == 0:
                output = result.stdout.strip()
                if "Physical size:" in output:
                    size_str = output.split("Physical size: ")[1]
                    if "x" in size_str:
                        width, height = map(int, size_str.split("x"))

            return {
                "width": width,
                "height": height,
                "density": self._get_screen_density(device_id)
            }

        except Exception as e:
            print(f"获取屏幕信息失败: {e}")
            return {"width": 1080, "height": 1920, "density": 2.0}

    def _get_screen_density(self, device_id: str) -> float:
        """获取屏幕密度"""
        try:
            result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "wm", "density"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                output = result.stdout.strip()
                if "Physical density:" in output:
                    density_str = output.split("Physical density: ")[1]
                    return float(density_str)

            return 2.0  # 默认密度

        except Exception as e:
            print(f"获取屏幕密度失败: {e}")
            return 2.0

    def _check_if_scrollable(self, dom_tree: Dict[str, Any]) -> bool:
        """检查页面是否可滚动"""
        def check_node(node):
            if "payload" in node and node["payload"].get("scrollable", False):
                return True
            if "children" in node:
                for child in node["children"]:
                    if check_node(child):
                        return True
            return False

        return check_node(dom_tree)

    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    # Android端操作方法
    async def click_by_coordinate(self, x: int, y: int, device_id: Optional[str] = None) -> Dict[str, Any]:
        """通过坐标点击"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 执行点击命令
            result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "input", "tap", str(x), str(y)],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"成功点击坐标 ({x}, {y})",
                    "action": "click",
                    "coordinates": {"x": x, "y": y}
                }
            else:
                return {"success": False, "message": f"点击失败: {result.stderr}"}

        except Exception as e:
            return {"success": False, "message": f"点击失败: {str(e)}"}

    async def click_by_seq_index(self, seq_index: int, device_id: Optional[str] = None) -> Dict[str, Any]:
        """通过seq_index点击元素"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 获取DOM树
            dom_data = await self.get_dom_tree_and_page_screenshot(device_id)
            if "error" in dom_data:
                return {"success": False, "message": dom_data["error"]}

            # 查找指定seq_index的元素
            element_info = self._find_element_by_seq_index(dom_data["dom_tree"], seq_index)
            if not element_info:
                return {"success": False, "message": f"未找到seq_index为{seq_index}的元素"}

            # 获取元素中心坐标
            rect = element_info.get("payload", {}).get("rect", {})
            if not rect:
                return {"success": False, "message": "元素没有位置信息"}

            center_x = rect["x"] + rect["width"] // 2
            center_y = rect["y"] + rect["height"] // 2

            # 点击元素中心
            click_result = await self.click_by_coordinate(center_x, center_y, device_id)
            if click_result["success"]:
                click_result.update({
                    "seq_index": seq_index,
                    "element_info": element_info
                })

            return click_result

        except Exception as e:
            return {"success": False, "message": f"点击失败: {str(e)}"}

    async def input_text(self, text: str, seq_index: Optional[int] = None, x: Optional[int] = None, y: Optional[int] = None, device_id: Optional[str] = None) -> Dict[str, Any]:
        """输入文本"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 如果提供了seq_index，先点击该元素
            if seq_index is not None:
                click_result = await self.click_by_seq_index(seq_index, device_id)
                if not click_result["success"]:
                    return click_result
            elif x is not None and y is not None:
                # 如果提供了坐标，先点击该位置
                click_result = await self.click_by_coordinate(x, y, device_id)
                if not click_result["success"]:
                    return click_result

            # 清空当前输入框内容
            clear_result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "input", "keyevent", "KEYCODE_CTRL_A"],
                capture_output=True,
                text=True,
                timeout=5
            )

            # 输入文本
            result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "input", "text", text],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"成功输入文本: {text}",
                    "action": "input",
                    "text": text,
                    "seq_index": seq_index,
                    "coordinates": {"x": x, "y": y} if x and y else None
                }
            else:
                return {"success": False, "message": f"输入文本失败: {result.stderr}"}

        except Exception as e:
            return {"success": False, "message": f"输入文本失败: {str(e)}"}

    async def scroll_page(self, direction: str = "down", distance: int = 500, device_id: Optional[str] = None) -> Dict[str, Any]:
        """滚动页面"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 获取屏幕信息
            screen_info = self._get_screen_info(device_id)
            center_x = screen_info["width"] // 2
            center_y = screen_info["height"] // 2

            # 计算滚动起点和终点
            if direction == "down":
                start_x, start_y = center_x, center_y + distance // 2
                end_x, end_y = center_x, center_y - distance // 2
            elif direction == "up":
                start_x, start_y = center_x, center_y - distance // 2
                end_x, end_y = center_x, center_y + distance // 2
            elif direction == "left":
                start_x, start_y = center_x + distance // 2, center_y
                end_x, end_y = center_x - distance // 2, center_y
            elif direction == "right":
                start_x, start_y = center_x - distance // 2, center_y
                end_x, end_y = center_x + distance // 2, center_y
            else:
                return {"success": False, "message": f"不支持的滚动方向: {direction}"}

            # 执行滑动命令
            result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "input", "swipe",
                 str(start_x), str(start_y), str(end_x), str(end_y), "300"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                # 等待滚动完成
                await asyncio.sleep(0.5)

                return {
                    "success": True,
                    "message": f"成功向{direction}滚动{distance}像素",
                    "action": "scroll",
                    "direction": direction,
                    "distance": distance
                }
            else:
                return {"success": False, "message": f"滚动失败: {result.stderr}"}

        except Exception as e:
            return {"success": False, "message": f"滚动失败: {str(e)}"}

    async def press_key(self, key: str, device_id: Optional[str] = None) -> Dict[str, Any]:
        """按键操作"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 映射按键名称到Android键码
            key_mapping = {
                "back": "KEYCODE_BACK",
                "home": "KEYCODE_HOME",
                "menu": "KEYCODE_MENU",
                "enter": "KEYCODE_ENTER",
                "delete": "KEYCODE_DEL",
                "backspace": "KEYCODE_DEL",
                "space": "KEYCODE_SPACE",
                "tab": "KEYCODE_TAB",
                "escape": "KEYCODE_ESCAPE",
                "up": "KEYCODE_DPAD_UP",
                "down": "KEYCODE_DPAD_DOWN",
                "left": "KEYCODE_DPAD_LEFT",
                "right": "KEYCODE_DPAD_RIGHT"
            }

            keycode = key_mapping.get(key.lower(), key)

            # 执行按键命令
            result = subprocess.run(
                [self.adb_path, "-s", device_id, "shell", "input", "keyevent", keycode],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"成功按下按键: {key}",
                    "action": "press_key",
                    "key": key,
                    "keycode": keycode
                }
            else:
                return {"success": False, "message": f"按键操作失败: {result.stderr}"}

        except Exception as e:
            return {"success": False, "message": f"按键操作失败: {str(e)}"}

    def _find_element_by_seq_index(self, node: Dict[str, Any], target_seq_index: int) -> Optional[Dict[str, Any]]:
        """在DOM树中查找指定seq_index的元素"""
        if node.get("seq_index") == target_seq_index:
            return node

        if "children" in node and node["children"]:
            for child in node["children"]:
                result = self._find_element_by_seq_index(child, target_seq_index)
                if result:
                    return result

        return None

    # 实时执行需要的方法
    async def get_current_device_status(self) -> Dict[str, Any]:
        """获取当前活动设备状态"""
        try:
            active_device = self.db.query(AndroidDevice).filter(
                AndroidDevice.is_active == True
            ).first()

            if not active_device:
                return {
                    "is_connected": False,
                    "message": "没有活动设备"
                }

            return {
                "is_connected": active_device.is_connected,
                "device_id": active_device.device_id,
                "device_name": active_device.device_name,
                "message": f"当前设备: {active_device.device_name}"
            }

        except Exception as e:
            return {
                "is_connected": False,
                "error": f"获取设备状态失败: {str(e)}"
            }

    async def execute_scroll_action(self, exec_code: str) -> Dict[str, Any]:
        """执行滚动操作"""
        try:
            # 解析执行代码中的滚动参数
            # 这里需要根据AI生成的代码来执行相应的滚动操作
            # 示例代码可能是: scroll_page("down", 300)

            # 简单的代码解析（实际应该更复杂）
            if "scroll_page" in exec_code:
                if "down" in exec_code:
                    direction = "down"
                elif "up" in exec_code:
                    direction = "up"
                else:
                    direction = "down"

                # 提取距离参数（如果有）
                import re
                distance_match = re.search(r'(\d+)', exec_code)
                distance = int(distance_match.group(1)) if distance_match else 500

                result = await self.scroll_page(direction, distance)
                return result
            else:
                return {
                    "success": False,
                    "error_message": "不支持的滚动操作代码"
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"执行滚动操作失败: {str(e)}"
            }

    async def execute_action(self, exec_code: str) -> Dict[str, Any]:
        """执行具体的操作动作"""
        try:
            # 解析并执行AI生成的操作代码
            # 代码可能包含点击、输入文本等操作

            if "click_by_seq_index" in exec_code or "clickByCoordinate" in exec_code:
                # 提取seq_index
                import re
                seq_match = re.search(r'(\d+)', exec_code)
                if seq_match:
                    seq_index = int(seq_match.group(1))
                    result = await self.click_by_seq_index(seq_index)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析点击操作的seq_index"
                    }

            elif "input_text" in exec_code:
                # 提取文本和seq_index
                import re
                # 匹配类似 input_text_by_coordinate(seq_index, "text")
                match = re.search(r'input_text.*?(\d+).*?"([^"]*)"', exec_code)
                if match:
                    seq_index = int(match.group(1))
                    text = match.group(2)
                    result = await self.input_text(text, seq_index=seq_index)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析输入文本操作的参数"
                    }

            elif "press_key" in exec_code:
                # 提取按键
                import re
                key_match = re.search(r'"([^"]*)"', exec_code)
                if key_match:
                    key = key_match.group(1)
                    result = await self.press_key(key)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析按键操作的参数"
                    }

            else:
                return {
                    "success": False,
                    "error_message": f"不支持的操作代码: {exec_code}"
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"执行操作失败: {str(e)}"
            }

    async def take_screenshot_for_execution(self, device_id: Optional[str] = None) -> Dict[str, Any]:
        """为实时执行获取设备截图"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {
                        "success": False,
                        "message": "没有活动设备"
                    }
                device_id = active_device.device_id

            # 调用原有的截图方法
            result = await self.take_screenshot(device_id)

            # 转换返回格式以符合实时执行的需求
            if result.get("success"):
                return {
                    "success": True,
                    "screenshot_path": result["file_path"],
                    "url": result["url"]
                }
            else:
                return {
                    "success": False,
                    "error_message": result.get("message", "截图失败")
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"获取截图失败: {str(e)}"
            }
