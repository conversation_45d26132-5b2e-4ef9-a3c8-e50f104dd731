/**
 * Android端API
 */

import { request } from '@/utils/request'

export interface DeviceInfo {
  device_id: string
  device_name: string
  brand?: string
  model?: string
  android_version?: string
  screen_resolution?: string
  is_connected: boolean
  is_active: boolean
}

export interface InstallApkRequest {
  download_url: string
  device_id?: string
}

export interface AdbCommandRequest {
  command: string
  device_id?: string
}

export interface AdbCommandResponse {
  success: boolean
  command: string
  stdout: string
  stderr: string
  return_code: number
  message?: string
}

export interface ScreenshotResponse {
  success: boolean
  file_path: string
  url: string
  message?: string
}

export const androidApi = {
  /**
   * 获取Android设备列表
   */
  getDevices: (): Promise<DeviceInfo[]> => {
    return request.get('/api/android/devices')
  },

  /**
   * 选择当前活动设备
   */
  selectDevice: (deviceId: string): Promise<{ success: boolean; message: string; device?: any }> => {
    return request.post(`/api/android/devices/${deviceId}/select`)
  },

  /**
   * 刷新设备列表
   */
  refreshDevices: (): Promise<{ success: boolean; message: string; devices: DeviceInfo[] }> => {
    return request.post('/api/android/devices/refresh')
  },

  /**
   * 创建APK安装任务（异步）
   */
  installApk: (data: InstallApkRequest): Promise<{ success: boolean; message: string; task_id?: string }> => {
    return request.post('/api/android/install_apk', data)
  },

  /**
   * 获取APK安装任务状态
   */
  getApkInstallStatus: (taskId: string): Promise<{
    success: boolean
    task_id?: string
    status?: string
    progress?: number
    filename?: string
    total_size?: number
    downloaded_size?: number
    download_speed?: string
    error_message?: string
    created_at?: string
    completed_at?: string
    message?: string
  }> => {
    return request.get(`/api/android/install_tasks/${taskId}/status`)
  },

  /**
   * 获取APK安装任务列表
   */
  getApkInstallTasks: (deviceId?: string): Promise<{
    success: boolean
    tasks: Array<{
      task_id: string
      status: string
      progress: number
      filename: string
      download_url: string
      device_id: string
      total_size?: number
      downloaded_size?: number
      download_speed?: string
      error_message?: string
      created_at?: string
      completed_at?: string
    }>
  }> => {
    const params = deviceId ? { device_id: deviceId } : {}
    return request.get('/api/android/install_tasks', { params })
  },

  /**
   * 执行ADB命令
   */
  executeAdbCommand: (data: AdbCommandRequest): Promise<AdbCommandResponse> => {
    return request.post('/api/android/adb_command', data)
  },

  /**
   * 获取设备截图
   */
  takeScreenshot: (deviceId: string): Promise<ScreenshotResponse> => {
    return request.get(`/api/android/devices/${deviceId}/screenshot`)
  },

  /**
   * 获取设备状态
   */
  getDeviceStatus: (deviceId: string): Promise<{ success: boolean; device?: any; message?: string }> => {
    return request.get(`/api/android/devices/${deviceId}/status`)
  },

  /**
   * 通过坐标点击
   */
  clickByCoordinate: (x: number, y: number, deviceId?: string): Promise<{ success: boolean; message: string }> => {
    return request.post('/api/android/click_coordinate', { x, y, device_id: deviceId })
  },

  /**
   * 通过seq_index点击元素
   */
  clickBySeqIndex: (seqIndex: number, deviceId?: string): Promise<{ success: boolean; message: string }> => {
    return request.post('/api/android/click_seq_index', { seq_index: seqIndex, device_id: deviceId })
  },

  /**
   * 输入文本
   */
  inputText: (text: string, options?: { seq_index?: number; x?: number; y?: number; device_id?: string }): Promise<{ success: boolean; message: string }> => {
    return request.post('/api/android/input_text', { text, ...options })
  },

  /**
   * 滚动页面
   */
  scrollPage: (direction: string = 'down', distance: number = 500, deviceId?: string): Promise<{ success: boolean; message: string }> => {
    return request.post('/api/android/scroll', { direction, distance, device_id: deviceId })
  },

  /**
   * 按键操作
   */
  pressKey: (key: string, deviceId?: string): Promise<{ success: boolean; message: string }> => {
    return request.post('/api/android/press_key', { key, device_id: deviceId })
  },

  /**
   * 获取DOM树和页面截图
   */
  getDomTreeAndScreenshot: (deviceId?: string): Promise<{
    base64_image: string
    viewport: any
    is_scrollable: boolean
    dom_tree: any
    screenshot_path: string
    error?: string
  }> => {
    return request.post('/api/android/get_dom_tree_and_screenshot', { device_id: deviceId })
  },

  /**
   * 获取poco DOM树
   */
  getPocoDomTree: (deviceId?: string): Promise<{
    success: boolean
    dom_tree?: any
    message: string
  }> => {
    const params = deviceId ? { device_id: deviceId } : {}
    return request.get('/api/android/poco_dom_tree', { params })
  },

  /**
   * 获取带poco DOM树标注的截图
   */
  getScreenshotWithPoco: (deviceId?: string): Promise<{
    success: boolean
    base64_image?: string
    dom_tree?: any
    screenshot_path?: string
    url?: string
    message: string
  }> => {
    const params = deviceId ? { device_id: deviceId } : {}
    return request.get('/api/android/screenshot_with_poco', { params })
  }
}
