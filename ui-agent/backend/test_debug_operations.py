#!/usr/bin/env python3
"""
测试调试操作功能的脚本
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_android_debug_operation():
    """测试Android端调试操作"""
    print("=== 测试Android端调试操作 ===")
    
    # 测试点击操作（通过坐标）
    click_payload = {
        "operation": "click",
        "x": 540,
        "y": 1170
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/android/debug_operation",
            json=click_payload,
            headers={"Content-Type": "application/json"}
        )
        
        result = response.json()
        print(f"点击操作结果: {result}")
        
        if result.get("success"):
            print("✓ Android点击操作测试成功")
        else:
            print(f"✗ Android点击操作测试失败: {result.get('message')}")
            
    except Exception as e:
        print(f"✗ Android点击操作测试异常: {e}")
    
    # 测试滑动操作
    swipe_payload = {
        "operation": "swipe",
        "direction": "down"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/android/debug_operation",
            json=swipe_payload,
            headers={"Content-Type": "application/json"}
        )
        
        result = response.json()
        print(f"滑动操作结果: {result}")
        
        if result.get("success"):
            print("✓ Android滑动操作测试成功")
        else:
            print(f"✗ Android滑动操作测试失败: {result.get('message')}")
            
    except Exception as e:
        print(f"✗ Android滑动操作测试异常: {e}")

def test_web_debug_operation():
    """测试Web端调试操作"""
    print("\n=== 测试Web端调试操作 ===")
    
    # 测试点击操作（通过坐标）
    click_payload = {
        "operation": "click",
        "x": 500,
        "y": 300
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/web/debug_operation",
            json=click_payload,
            headers={"Content-Type": "application/json"}
        )
        
        result = response.json()
        print(f"点击操作结果: {result}")
        
        if result.get("success"):
            print("✓ Web点击操作测试成功")
        else:
            print(f"✗ Web点击操作测试失败: {result.get('message')}")
            
    except Exception as e:
        print(f"✗ Web点击操作测试异常: {e}")
    
    # 测试滚动操作
    scroll_payload = {
        "operation": "scroll",
        "direction": "down"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/web/debug_operation",
            json=scroll_payload,
            headers={"Content-Type": "application/json"}
        )
        
        result = response.json()
        print(f"滚动操作结果: {result}")
        
        if result.get("success"):
            print("✓ Web滚动操作测试成功")
        else:
            print(f"✗ Web滚动操作测试失败: {result.get('message')}")
            
    except Exception as e:
        print(f"✗ Web滚动操作测试异常: {e}")

def test_input_operation():
    """测试输入操作"""
    print("\n=== 测试输入操作 ===")
    
    # 测试Android输入操作
    android_input_payload = {
        "operation": "input",
        "x": 540,
        "y": 500,
        "text": "测试文本"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/android/debug_operation",
            json=android_input_payload,
            headers={"Content-Type": "application/json"}
        )
        
        result = response.json()
        print(f"Android输入操作结果: {result}")
        
        if result.get("success"):
            print("✓ Android输入操作测试成功")
        else:
            print(f"✗ Android输入操作测试失败: {result.get('message')}")
            
    except Exception as e:
        print(f"✗ Android输入操作测试异常: {e}")
    
    # 测试Web输入操作
    web_input_payload = {
        "operation": "input",
        "x": 400,
        "y": 200,
        "text": "测试文本"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/web/debug_operation",
            json=web_input_payload,
            headers={"Content-Type": "application/json"}
        )
        
        result = response.json()
        print(f"Web输入操作结果: {result}")
        
        if result.get("success"):
            print("✓ Web输入操作测试成功")
        else:
            print(f"✗ Web输入操作测试失败: {result.get('message')}")
            
    except Exception as e:
        print(f"✗ Web输入操作测试异常: {e}")

def test_invalid_operations():
    """测试无效操作"""
    print("\n=== 测试无效操作 ===")
    
    # 测试缺少参数的操作
    invalid_payload = {
        "operation": "click"
        # 缺少坐标或索引
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/android/debug_operation",
            json=invalid_payload,
            headers={"Content-Type": "application/json"}
        )
        
        result = response.json()
        print(f"无效操作结果: {result}")
        
        if not result.get("success"):
            print("✓ 无效操作正确返回错误")
        else:
            print("✗ 无效操作应该返回错误")
            
    except Exception as e:
        print(f"✗ 无效操作测试异常: {e}")

if __name__ == "__main__":
    print("开始测试调试操作功能...")
    
    # 测试Android端操作
    test_android_debug_operation()
    
    # 测试Web端操作
    test_web_debug_operation()
    
    # 测试输入操作
    test_input_operation()
    
    # 测试无效操作
    test_invalid_operations()
    
    print("\n🎉 调试操作功能测试完成！")
