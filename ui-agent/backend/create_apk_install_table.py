#!/usr/bin/env python3
"""
创建APK安装任务表的迁移脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from app.core.config import settings
from app.models.device import Base

def create_tables():
    """创建数据库表"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        print("✅ APK安装任务表创建成功！")
        print("新增表: apk_install_tasks")
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 开始创建APK安装任务表...")
    success = create_tables()
    
    if success:
        print("🎉 数据库迁移完成！")
    else:
        print("💥 数据库迁移失败！")
        sys.exit(1)
