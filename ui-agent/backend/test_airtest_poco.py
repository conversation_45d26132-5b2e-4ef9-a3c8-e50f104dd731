#!/usr/bin/env python3
"""
测试airtest+poco功能的脚本
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_airtest_poco_import():
    """测试airtest和poco的导入"""
    print("=== 测试airtest和poco导入 ===")
    
    try:
        from airtest.core.api import connect_device, touch, swipe, snapshot, keyevent, text as text_input
        print("✓ airtest.core.api 导入成功")
    except ImportError as e:
        print(f"✗ airtest.core.api 导入失败: {e}")
        return False
    
    try:
        from airtest.core.android.adb import ADB
        print("✓ airtest.core.android.adb 导入成功")
    except ImportError as e:
        print(f"✗ airtest.core.android.adb 导入失败: {e}")
        return False
    
    try:
        from airtest.core.settings import Settings as AirtestSettings
        print("✓ airtest.core.settings 导入成功")
    except ImportError as e:
        print(f"✗ airtest.core.settings 导入失败: {e}")
        return False
    
    try:
        from poco.drivers.android.uiautomation import AndroidUiautomationPoco
        print("✓ poco.drivers.android.uiautomation 导入成功")
    except ImportError as e:
        print(f"✗ poco.drivers.android.uiautomation 导入失败: {e}")
        return False
    
    return True

def test_device_connection():
    """测试设备连接"""
    print("\n=== 测试设备连接 ===")
    
    try:
        from airtest.core.api import connect_device
        from airtest.core.android.adb import ADB
        
        # 获取连接的设备列表
        adb = ADB()
        devices = adb.devices()
        print(f"发现设备: {devices}")

        if not devices:
            print("✗ 没有发现连接的设备")
            return False

        # 尝试连接第一个设备
        if isinstance(devices, list):
            device_id = devices[0][0]  # 获取设备ID
        else:
            device_id = list(devices.keys())[0]
        print(f"尝试连接设备: {device_id}")
        
        device_uri = f"android:///{device_id}"
        device = connect_device(device_uri)
        print(f"✓ 成功连接设备: {device}")
        
        return True
        
    except Exception as e:
        print(f"✗ 设备连接失败: {e}")
        return False

def test_poco_instance():
    """测试poco实例创建"""
    print("\n=== 测试poco实例创建 ===")
    
    try:
        from airtest.core.api import connect_device
        from poco.drivers.android.uiautomation import AndroidUiautomationPoco
        from airtest.core.android.adb import ADB
        
        # 获取设备
        adb = ADB()
        devices = adb.devices()

        if not devices:
            print("✗ 没有发现连接的设备")
            return False

        # 获取设备ID
        if isinstance(devices, list):
            device_id = devices[0][0]  # 获取设备ID
        else:
            device_id = list(devices.keys())[0]
        device_uri = f"android:///{device_id}"
        device = connect_device(device_uri)
        
        # 创建poco实例
        poco = AndroidUiautomationPoco(device=device, use_airtest_input=True, screenshot_each_action=False)
        print(f"✓ 成功创建poco实例: {poco}")
        
        # 测试获取UI树
        try:
            ui_tree = poco.agent.hierarchy.dump()
            print(f"✓ 成功获取UI树，根节点: {ui_tree.get('name', 'Unknown')}")
            return True
        except Exception as ui_error:
            print(f"✗ 获取UI树失败: {ui_error}")
            return False
        
    except Exception as e:
        print(f"✗ poco实例创建失败: {e}")
        return False

def test_android_service():
    """测试AndroidService"""
    print("\n=== 测试AndroidService ===")
    
    try:
        from app.core.database import SessionLocal
        from app.services.android_service import AndroidService
        
        # 创建数据库会话
        db = SessionLocal()
        
        # 创建AndroidService实例
        android_service = AndroidService(db)
        print("✓ AndroidService实例创建成功")
        
        # 测试设备刷新
        import asyncio
        
        async def test_refresh():
            devices = await android_service.refresh_devices()
            print(f"✓ 设备刷新成功，发现 {len(devices)} 个设备")
            return devices
        
        devices = asyncio.run(test_refresh())
        
        if devices:
            device_id = devices[0]['device_id']
            print(f"测试设备: {device_id}")
            
            # 测试poco DOM树获取
            async def test_poco_dom():
                result = await android_service.get_poco_dom_tree(device_id)
                return result
            
            dom_result = asyncio.run(test_poco_dom())
            if dom_result.get('success'):
                print("✓ poco DOM树获取成功")
            else:
                print(f"✗ poco DOM树获取失败: {dom_result.get('message')}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ AndroidService测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试airtest+poco功能...")
    
    # 测试导入
    if not test_airtest_poco_import():
        print("\n❌ 导入测试失败，请检查airtest和poco安装")
        sys.exit(1)
    
    # 测试设备连接
    if not test_device_connection():
        print("\n❌ 设备连接测试失败，请检查设备连接")
        sys.exit(1)
    
    # 测试poco实例
    if not test_poco_instance():
        print("\n❌ poco实例测试失败")
        sys.exit(1)
    
    # 测试AndroidService
    if not test_android_service():
        print("\n❌ AndroidService测试失败")
        sys.exit(1)
    
    print("\n🎉 所有测试通过！airtest+poco功能正常")
