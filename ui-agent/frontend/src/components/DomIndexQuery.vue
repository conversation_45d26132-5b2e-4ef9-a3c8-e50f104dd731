<template>
  <div class="dom-index-query">
    <div class="query-header">
      <h3>DOM元素索引查询</h3>
      <p class="description">输入元素的序列索引来查询对应的DOM元素信息</p>
    </div>
    
    <div class="query-form">
      <el-form :model="queryForm" inline>
        <el-form-item label="索引序号:">
          <el-input-number
            v-model="queryForm.seqIndex"
            :min="0"
            :max="9999"
            placeholder="请输入索引序号"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item>
          <el-button 
            type="primary" 
            @click="queryElement"
            :loading="loading"
            :disabled="queryForm.seqIndex === null || queryForm.seqIndex === undefined"
          >
            查询元素
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="elementInfo" class="element-result">
      <div class="result-header">
        <h4>查询结果</h4>
        <el-tag :type="elementInfo.success ? 'success' : 'danger'">
          {{ elementInfo.success ? '查询成功' : '查询失败' }}
        </el-tag>
      </div>
      
      <div v-if="elementInfo.success && elementInfo.element" class="element-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="索引序号">
            {{ elementInfo.element.seq_index }}
          </el-descriptions-item>
          <el-descriptions-item label="元素类型">
            {{ elementInfo.element.payload?.type || elementInfo.element.payload?.name || 'Unknown' }}
          </el-descriptions-item>
          <el-descriptions-item label="文本内容" :span="2">
            <span v-if="elementInfo.element.payload?.text" class="text-content">
              {{ elementInfo.element.payload.text }}
            </span>
            <span v-else class="no-text">无文本内容</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否可点击">
            <el-tag :type="elementInfo.element.payload?.clickable ? 'success' : 'info'">
              {{ elementInfo.element.payload?.clickable ? '可点击' : '不可点击' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否启用">
            <el-tag :type="elementInfo.element.payload?.enabled ? 'success' : 'warning'">
              {{ elementInfo.element.payload?.enabled ? '已启用' : '已禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="位置信息" :span="2">
            <div v-if="elementInfo.element.payload?.pos" class="position-info">
              <span>中心点: ({{ formatPosition(elementInfo.element.payload.pos[0]) }}, {{ formatPosition(elementInfo.element.payload.pos[1]) }})</span>
            </div>
            <div v-else-if="elementInfo.element.payload?.rect" class="position-info">
              <span>坐标: ({{ elementInfo.element.payload.rect.x }}, {{ elementInfo.element.payload.rect.y }})</span>
              <span>大小: {{ elementInfo.element.payload.rect.width }} × {{ elementInfo.element.payload.rect.height }}</span>
            </div>
            <span v-else class="no-position">无位置信息</span>
          </el-descriptions-item>
          <el-descriptions-item label="大小信息" :span="2">
            <div v-if="elementInfo.element.payload?.size" class="size-info">
              <span>相对大小: {{ formatSize(elementInfo.element.payload.size[0]) }} × {{ formatSize(elementInfo.element.payload.size[1]) }}</span>
            </div>
            <span v-else class="no-size">无大小信息</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="element-actions">
          <el-button 
            v-if="elementInfo.element.payload?.clickable" 
            type="primary" 
            @click="clickElement"
            :loading="clickLoading"
          >
            点击此元素
          </el-button>
          <el-button 
            type="info" 
            @click="showFullDetails"
          >
            查看完整信息
          </el-button>
        </div>
      </div>
      
      <div v-else class="error-message">
        <el-alert
          :title="elementInfo.message"
          type="warning"
          :closable="false"
        />
      </div>
    </div>

    <!-- 完整信息对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="元素完整信息"
      width="60%"
      :before-close="closeDetailsDialog"
    >
      <div class="full-details">
        <pre>{{ JSON.stringify(elementInfo?.element, null, 2) }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  type: 'web' | 'android'
  deviceId?: string
}

const props = defineProps<Props>()
const emit = defineEmits(['element-click'])

const loading = ref(false)
const clickLoading = ref(false)
const showDetailsDialog = ref(false)

const queryForm = reactive({
  seqIndex: null as number | null
})

const elementInfo = ref<any>(null)

const queryElement = async () => {
  if (queryForm.seqIndex === null || queryForm.seqIndex === undefined) {
    ElMessage.warning('请输入有效的索引序号')
    return
  }

  loading.value = true
  try {
    let result
    if (props.type === 'web') {
      const { webApi } = await import('@/api/web')
      result = await webApi.getDomElementByIndex(queryForm.seqIndex)
    } else {
      const { androidApi } = await import('@/api/android')
      result = await androidApi.getDomElementByIndex(queryForm.seqIndex, props.deviceId)
    }

    elementInfo.value = result
    
    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.warning(result.message)
    }
  } catch (error) {
    ElMessage.error('查询元素失败')
    elementInfo.value = {
      success: false,
      message: '查询请求失败'
    }
  } finally {
    loading.value = false
  }
}

const clickElement = async () => {
  if (!elementInfo.value?.element) return

  clickLoading.value = true
  try {
    emit('element-click', elementInfo.value.element)
    ElMessage.success('元素点击指令已发送')
  } catch (error) {
    ElMessage.error('点击元素失败')
  } finally {
    clickLoading.value = false
  }
}

const showFullDetails = () => {
  showDetailsDialog.value = true
}

const closeDetailsDialog = () => {
  showDetailsDialog.value = false
}

const formatPosition = (value: number): string => {
  return (value * 100).toFixed(1) + '%'
}

const formatSize = (value: number): string => {
  return (value * 100).toFixed(1) + '%'
}
</script>

<style scoped>
.dom-index-query {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.query-header {
  margin-bottom: 16px;
}

.query-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.query-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.element-result {
  flex: 1;
  overflow-y: auto;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-header h4 {
  margin: 0;
  color: #303133;
}

.element-details {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.text-content {
  color: #303133;
  word-break: break-all;
}

.no-text, .no-position, .no-size {
  color: #909399;
  font-style: italic;
}

.position-info, .size-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.element-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.error-message {
  margin-top: 16px;
}

.full-details {
  max-height: 400px;
  overflow-y: auto;
  background: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
}

.full-details pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #303133;
}
</style>
