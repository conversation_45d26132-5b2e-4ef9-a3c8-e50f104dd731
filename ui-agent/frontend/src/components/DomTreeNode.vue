<template>
  <div class="dom-tree-node">
    <div 
      class="node-content" 
      :class="{ 
        'clickable': isClickable,
        'has-text': hasText,
        'expanded': isExpanded 
      }"
      :style="{ paddingLeft: `${level * 20}px` }"
      @click="toggleExpand"
    >
      <!-- 展开/收起图标 -->
      <span v-if="hasChildren" class="expand-icon">
        <el-icon v-if="isExpanded"><ArrowDown /></el-icon>
        <el-icon v-else><ArrowRight /></el-icon>
      </span>
      <span v-else class="expand-placeholder"></span>
      
      <!-- 节点信息 -->
      <div class="node-info">
        <span class="node-index">{{ node.seq_index }}</span>
        <span class="node-type">{{ nodeType }}</span>
        <span v-if="nodeText" class="node-text">{{ nodeText }}</span>
        <span v-if="nodeSize" class="node-size">{{ nodeSize }}</span>
      </div>
      
      <!-- 操作按钮 -->
      <div class="node-actions">
        <el-button 
          v-if="isClickable" 
          size="small" 
          type="primary" 
          @click.stop="onNodeClick"
        >
          点击
        </el-button>
        <el-button 
          size="small" 
          type="info" 
          @click.stop="showDetails"
        >
          详情
        </el-button>
      </div>
    </div>
    
    <!-- 子节点 -->
    <div v-if="isExpanded && hasChildren" class="children">
      <DomTreeNode
        v-for="(child, index) in node.children"
        :key="`${child.seq_index || index}`"
        :node="child"
        :level="level + 1"
        @node-click="$emit('node-click', $event)"
        ref="childNodes"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, defineExpose } from 'vue'
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue'

interface Props {
  node: any
  level: number
}

const props = defineProps<Props>()
const emit = defineEmits(['node-click'])

const isExpanded = ref(false)
const childNodes = ref<any[]>([])

// 计算属性
const hasChildren = computed(() => {
  return props.node.children && props.node.children.length > 0
})

const isClickable = computed(() => {
  return props.node.payload?.clickable || props.node.payload?.enabled
})

const hasText = computed(() => {
  return props.node.payload?.text && props.node.payload.text.trim()
})

const nodeType = computed(() => {
  return props.node.payload?.type || props.node.payload?.name || 'Unknown'
})

const nodeText = computed(() => {
  const text = props.node.payload?.text
  if (!text || !text.trim()) return ''
  return text.length > 30 ? text.substring(0, 30) + '...' : text
})

const nodeSize = computed(() => {
  const size = props.node.payload?.size
  if (!size || !Array.isArray(size) || size.length !== 2) return ''
  return `${Math.round(size[0] * 100)}%×${Math.round(size[1] * 100)}%`
})

// 方法
const toggleExpand = () => {
  if (hasChildren.value) {
    isExpanded.value = !isExpanded.value
  }
}

const onNodeClick = () => {
  emit('node-click', props.node)
}

const showDetails = () => {
  console.log('节点详情:', props.node)
  ElMessage.info('节点详情已输出到控制台')
}

const expandAll = () => {
  if (hasChildren.value) {
    isExpanded.value = true
    // 递归展开所有子节点
    setTimeout(() => {
      childNodes.value.forEach(child => {
        if (child && child.expandAll) {
          child.expandAll()
        }
      })
    }, 0)
  }
}

const collapseAll = () => {
  isExpanded.value = false
  // 递归收起所有子节点
  childNodes.value.forEach(child => {
    if (child && child.collapseAll) {
      child.collapseAll()
    }
  })
}

// 暴露方法给父组件
defineExpose({
  expandAll,
  collapseAll
})
</script>

<style scoped>
.dom-tree-node {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 2px 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.node-content:hover {
  background-color: #f5f5f5;
}

.node-content.clickable {
  border-left: 3px solid #409eff;
}

.node-content.has-text {
  border-left: 3px solid #67c23a;
}

.node-content.expanded {
  background-color: #f0f9ff;
}

.expand-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  color: #666;
}

.expand-placeholder {
  width: 16px;
  margin-right: 4px;
}

.node-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.node-index {
  background: #e1f5fe;
  color: #0277bd;
  padding: 1px 4px;
  border-radius: 2px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.node-type {
  color: #666;
  font-weight: 500;
}

.node-text {
  color: #333;
  background: #f9f9f9;
  padding: 1px 4px;
  border-radius: 2px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-size {
  color: #999;
  font-size: 10px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.node-content:hover .node-actions {
  opacity: 1;
}

.children {
  border-left: 1px dashed #ddd;
  margin-left: 8px;
}
</style>
