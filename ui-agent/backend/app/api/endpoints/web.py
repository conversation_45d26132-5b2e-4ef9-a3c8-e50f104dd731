"""
Web端API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.web_service import WebService

router = APIRouter()

# 全局Web服务实例，确保浏览器状态共享
_web_service_instance = None

def get_web_service(db: Session = Depends(get_db)) -> WebService:
    """获取Web服务实例（单例模式）"""
    global _web_service_instance
    if _web_service_instance is None:
        _web_service_instance = WebService(db)
    else:
        # 更新数据库会话
        _web_service_instance.db = db
    return _web_service_instance

class NavigateRequest(BaseModel):
    """导航请求模型"""
    url: str
    cookies: Optional[List[Dict[str, Any]]] = None
    headers: Optional[Dict[str, str]] = None
    is_h5_mode: bool = False

class SetCookiesRequest(BaseModel):
    """设置Cookie请求模型"""
    cookies: List[Dict[str, Any]]

class SetHeadersRequest(BaseModel):
    """设置Header请求模型"""
    headers: Dict[str, str]

class ExecuteJsRequest(BaseModel):
    """执行JavaScript请求模型"""
    code: str

@router.post("/navigate")
async def navigate_to_url(request: NavigateRequest, web_service: WebService = Depends(get_web_service)):
    """
    导航到指定URL
    """
    try:
        result = await web_service.navigate(
            url=request.url,
            cookies=request.cookies,
            headers=request.headers,
            is_h5_mode=request.is_h5_mode
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导航失败: {str(e)}")

@router.post("/cookies")
async def set_cookies(request: SetCookiesRequest, web_service: WebService = Depends(get_web_service)):
    """
    设置Cookie
    """
    try:
        result = await web_service.set_cookies(request.cookies)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置Cookie失败: {str(e)}")

@router.post("/headers")
async def set_headers(request: SetHeadersRequest, web_service: WebService = Depends(get_web_service)):
    """
    设置Header
    """
    try:
        result = await web_service.set_headers(request.headers)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置Header失败: {str(e)}")

@router.post("/execute_js")
async def execute_javascript(request: ExecuteJsRequest, web_service: WebService = Depends(get_web_service)):
    """
    执行JavaScript代码
    """
    try:
        result = await web_service.execute_javascript(request.code)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行JavaScript失败: {str(e)}")

@router.post("/reset")
async def reset_browser(web_service: WebService = Depends(get_web_service)):
    """
    重置浏览器实例
    """
    try:
        result = await web_service.reset_browser()
        # 重置后清除全局实例
        global _web_service_instance
        _web_service_instance = None
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置浏览器失败: {str(e)}")

@router.get("/screenshot")
async def take_screenshot(web_service: WebService = Depends(get_web_service)):
    """
    获取页面截图
    """
    try:
        result = await web_service.take_screenshot()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取截图失败: {str(e)}")

@router.get("/status")
async def get_browser_status(web_service: WebService = Depends(get_web_service)):
    """
    获取浏览器状态
    """
    try:
        status = await web_service.get_browser_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取浏览器状态失败: {str(e)}")

@router.get("/current_url")
async def get_current_url(web_service: WebService = Depends(get_web_service)):
    """
    获取当前页面URL
    """
    try:
        result = await web_service.get_current_url()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取当前URL失败: {str(e)}")

@router.get("/screenshot_with_dom")
async def take_screenshot_with_dom(web_service: WebService = Depends(get_web_service)):
    """
    获取带DOM树标注的页面截图
    """
    try:
        result = await web_service.get_dom_tree_and_page_screenshot()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取带DOM树标注的截图失败: {str(e)}")

@router.get("/dom_tree")
async def get_dom_tree(web_service: WebService = Depends(get_web_service)):
    """
    获取页面DOM树
    """
    try:
        dom_tree = await web_service._get_dom_tree()
        return {
            "success": True,
            "dom_tree": dom_tree,
            "message": "DOM树获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取DOM树失败: {str(e)}")

class DomElementQueryRequest(BaseModel):
    """DOM元素查询请求模型"""
    seq_index: int

@router.post("/dom_element")
async def get_dom_element_by_index(request: DomElementQueryRequest, web_service: WebService = Depends(get_web_service)):
    """
    根据索引查询DOM元素信息
    """
    try:
        result = await web_service.get_dom_element_by_index(request.seq_index)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询DOM元素失败: {str(e)}")

class WebDebugOperationRequest(BaseModel):
    """Web调试操作请求模型"""
    operation: str  # click, input, scroll
    seq_index: Optional[int] = None
    x: Optional[int] = None
    y: Optional[int] = None
    text: Optional[str] = None
    direction: Optional[str] = None  # up, down, left, right

@router.post("/debug_operation")
async def execute_web_debug_operation(request: WebDebugOperationRequest, web_service: WebService = Depends(get_web_service)):
    """
    执行Web调试操作（点击、输入、滚动）
    """
    try:
        if request.operation == "click":
            if request.seq_index is not None:
                result = await web_service.click_by_seq_index(request.seq_index)
            elif request.x is not None and request.y is not None:
                result = await web_service.click_by_coordinate(request.x, request.y)
            else:
                return {"success": False, "message": "点击操作需要提供seq_index或坐标(x,y)"}

        elif request.operation == "input":
            if not request.text:
                return {"success": False, "message": "输入操作需要提供text参数"}

            if request.seq_index is not None:
                result = await web_service.input_text_by_seq_index(request.seq_index, request.text)
            elif request.x is not None and request.y is not None:
                result = await web_service.input_text_by_coordinate(request.x, request.y, request.text)
            else:
                return {"success": False, "message": "输入操作需要提供seq_index或坐标(x,y)"}

        elif request.operation == "scroll":
            if not request.direction:
                return {"success": False, "message": "滚动操作需要提供direction参数"}

            result = await web_service.scroll_page(request.direction)

        else:
            return {"success": False, "message": f"不支持的操作类型: {request.operation}"}

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行Web调试操作失败: {str(e)}")
